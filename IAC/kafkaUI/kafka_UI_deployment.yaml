apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-ui
  namespace: microservices
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-ui
  template:
    metadata:
      labels:
        app: kafka-ui
    spec:
      serviceAccountName: microservices-sa
      containers:
      - name: kafka-ui
        image: provectuslabs/kafka-ui:latest
        ports:
        - containerPort: 8080
        env:
        - name: KAFKA_CLUSTERS_0_NAME
          value: "local"
        - name: KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS
          value: "b-1.devmsk.wg1c10.c2.kafka.eu-west-2.amazonaws.com:9098,b-2.devmsk.wg1c10.c2.kafka.eu-west-2.amazonaws.com:9098,b-3.devmsk.wg1c10.c2.kafka.eu-west-2.amazonaws.com:9098"
        - name: KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL
          value: "SASL_SSL"
        - name: KAFKA_CLUSTERS_0_PROPERTIES_SASL_MECHANISM
          value: "AWS_MSK_IAM"
        - name: KAFKA_CLUSTERS_0_PROPERTIES_SASL_CLIENT_CALLBACK_HANDLER_CLASS
          value: "software.amazon.msk.auth.iam.IAMClientCallbackHandler"
        - name: KAFKA_CLUSTERS_0_PROPERTIES_SASL_JAAS_CONFIG
          value: "software.amazon.msk.auth.iam.IAMLoginModule required;"
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-ui-service
  namespace: default
spec:
  selector:
    app: kafka-ui
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
---
# Source: microservices/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: microservices-testing
  labels:
    name: microservices-testing
    environment: dev
    istio.io/dataplane-mode: "ambient"
---
# Source: microservices/templates/serviceAccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: microservices-sa
  namespace: microservices-testing
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/dev-eks-msk-irsa-role
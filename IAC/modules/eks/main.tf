#IAC/modules/eks/main.tf
################################################################################
#EKS
################################################################################

module "eks" {
  source = "terraform-aws-modules/eks/aws"
  version = "20.33.0"

  cluster_name                   = var.cluster_name
  cluster_version                = var.cluster_version
  cluster_endpoint_public_access = true
  cluster_endpoint_private_access = var.cluster_endpoint_private_access

    cluster_addons = {
    coredns = {
      most_recent = true
    }
    kube-proxy = {
      most_recent = true
    }
    eks-pod-identity-agent = {
    most_recent = true
    }
    vpc-cni = {
      most_recent = true
      configuration_values = jsonencode({
    env = {
      ENABLE_PREFIX_DELEGATION = "true"
      WARM_ENI_TARGET = "2"
    }
  })
    }
    /*amazon-cloudwatch-observability = {
      most_recent = true
      configuration_values = jsonencode({
        agent = {
          config = {
            logs = {
              metrics_collected = {
                kubernetes = {
                  enhanced_container_insights = false
                }
              }
            }
          }
        }
        containerLogs = {
          enabled = false
        }
      })
    }*/
  }

  vpc_id                   = var.vpc_id
  subnet_ids               = var.subnet_ids
  control_plane_subnet_ids = var.control_plane_subnet_ids

  create_kms_key            = false
  cluster_encryption_config = {}
  enable_cluster_creator_admin_permissions = true

  # EKS Managed Node Group(s)
  eks_managed_node_group_defaults = {
    ami_type = "AL2_x86_64"
  }

  eks_managed_node_groups = { for key, val in var.instance_details : key => {
    min_size     = val.min_size
    max_size     = val.max_size
    desired_size = val.desired_size

    instance_types = val.instance_types

    update_config = {
      max_unavailable = 1 # or set `max_unavailable`
    }
      labels = {
        criticality = "high"
        "karpenter.sh/controller" = "true"
      }
    iam_role_additional_policies = {
        ContainerInsightsPolicy  = aws_iam_policy.container_insights_policy.arn
        AmazonSSMManagedInstanceCore = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
      }
  } }  
  enable_irsa = true
     node_security_group_additional_rules = {
    ingress_cluster_15017 = {
      description                   = "Cluster API to node groups on 15017 (Istio)"
      protocol                      = "tcp"
      from_port                     = 15017
      to_port                       = 15017
      type                          = "ingress"
      source_cluster_security_group = true
    }
   }
   node_security_group_tags = {
    "kubernetes.io/cluster/${var.cluster_name}" = "owned"
    "karpenter.sh/discovery" = var.cluster_name
  }
}
  resource "aws_iam_policy" "container_insights_policy" {
  name        = "eks-container-insights-policy-${var.cluster_name}"
  path        = "/"
  description = "IAM policy for EKS Container Insights"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:PutMetricData",
          "ec2:DescribeVolumes",
          "ec2:DescribeTags",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams",
          "logs:DescribeLogGroups",
          "logs:CreateLogStream",
          "logs:CreateLogGroup"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParameter"
        ]
        Resource = "arn:aws:ssm:*:*:parameter/AmazonCloudWatch-*"
      }
    ]
  })
  
}




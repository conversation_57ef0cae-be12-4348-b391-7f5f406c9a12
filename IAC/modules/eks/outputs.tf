#IAC/modules/eks/outputs.tf
output "cluster_security_group_id" {
  description = "Security group ids attached to the cluster control plane"
  value       = module.eks.cluster_security_group_id
}

output "node_security_group_id" {
  description = "Security group ids attached to node"
  value       = module.eks.node_security_group_id
}

output "cluster_name" {
  description = "cluster name"
  value       = module.eks.cluster_name
}

output "oidc_provider_arn" {
  description = "cluster oidc_provider_arn"
  value       = module.eks.oidc_provider_arn
}

output "oidc_provider" {
  description = "cluster oidc_provider_arn"
  value       = module.eks.oidc_provider
}
output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = module.eks.cluster_endpoint
}

output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster for the OpenID Connect identity provider"
  value       = module.eks.cluster_oidc_issuer_url
}
output "node_iam_role_arn" {
  description = "IAM role ARN for EKS node group"
  value = values(module.eks.eks_managed_node_groups)[0].iam_role_arn
}

output "node_iam_role_name" {
  description = "IAM role name for EKS node group"
  value = values(module.eks.eks_managed_node_groups)[0].iam_role_name
}
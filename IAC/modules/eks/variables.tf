#IAC/modules/eks/variables.tf
variable "vpc_id" {
  description = "Name of the vpc_id"
  type        = string
}

variable "subnet_ids" {
  description = "subnet ids for vpc"
  type        = list(string)
}

variable "control_plane_subnet_ids" {
  description = "subnet ids for the EKS control plane"
  type        = list(string)
}

variable "cluster_name" {
  description = "EKS Cluster Name"
  type        = string
  nullable    = false
}

variable "cluster_version" {
  description = "EKS Cluster Version"
  type        = string
  nullable    = false
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "instance_details" {
  description = "instance details"
  type = map(object({
    instance_types = list(string)
    min_size      = string
    max_size      = string
    desired_size  = string
  }))
}

variable "cluster_endpoint_private_access" {
  description = "Cluster endpoint private access"
  default = true
  type = string
}
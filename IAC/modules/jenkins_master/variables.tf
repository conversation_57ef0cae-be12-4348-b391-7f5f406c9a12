# Environment and Account Configuration
variable "name_prefix" {
  description = "Prefix to use for resource names"
  type        = string
}

variable "aws_region" {
  description = "AWS region where resources will be created"
  type        = string
}

variable "account_id" {
  description = "AWS account ID where resources will be created"
  type        = string
}

# Network Configuration
variable "vpc_id" {
  description = "ID of the VPC where <PERSON> will be deployed"
  type        = string
}

variable "subnet_ids" {
  description = "List of private subnet IDs where Jenkins instances will be deployed"
  type        = list(string)
}

variable "public_subnet_ids" {
  description = "List of public subnet IDs where the ALB will be deployed"
  type        = list(string)
}

# Instance Configuration
variable "instance_type" {
  description = "EC2 instance type for <PERSON>"
  type        = string
  default     = "t3.medium"
}

variable "root_volume_size" {
  description = "Size in GB for Jenkins root EBS volume"
  type        = number
  default     = 50
  validation {
    condition     = var.root_volume_size >= 20
    error_message = "Root volume size must be at least 20 GB."
  }
}

# Secrets Configuration
variable "jenkins_tailscale_secret_name" {
  description = "Name of the secret containing the Tailscale authentication key"
  type        = string
  default     = "jenkins-tailscale-auth-key"
}

variable "jenkins_admin_secret_name" {
  description = "Name of the secret where Jenkins admin password will be stored"
  type        = string
  default     = "jenkins-initial-admin-password"
}

# Backup Configuration
variable "backup_retention_days" {
  description = "Number of days to retain Jenkins backups"
  type        = number
  default     = 30
  validation {
    condition     = var.backup_retention_days >= 7
    error_message = "Backup retention period must be at least 7 days."
  }
}

# Load Balancer Configuration
variable "certificate_arn" {
  description = "ARN of the ACM certificate for Jenkins HTTPS listener"
  type        = string
}

variable "enable_deletion_protection" {
  description = "Whether to enable deletion protection for the ALB"
  type        = bool
  default     = true
}

variable "office_cidrs" {
  description = "List of office CIDRs allowed to access Jenkins"
  type        = list(string)
  default     = []
}

# Additional Tags
variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "master_userdata_script_version" {
  description = "Version of the script to be used"
  type        = string
  default     = "0.1"
}

variable "bitbucket_cidrs" {
  description = "List of Bitbucket CIDR blocks for webhook access"
  type        = list(string)
  default     = [
    "*************/21",     # Main Atlassian network range
    "*************/22",     # Additional Atlassian range
    "*************/25"      # New AWS range for Atlassian services
  ]
}

# Monitoring Configuration
variable "alert_emails" {
  description = "List of email addresses for CloudWatch alerts"
  type        = list(string)
  default     = []
}

variable "monitoring_jmx_port" {
  description = "Port for JMX monitoring"
  type        = number
  default     = 9010
}

variable "monitoring_jmx_prometheus_port" {
  description = "Port for JMX Prometheus monitoring"
  type        = number
  default     = 9011
}

#variables for jenkins_bitbucket integration
variable "jenkins_bitbucket_private_key_secret" {
  description = "Name of the AWS Secrets Manager secret to store Jenkins SSH private key"
  type        = string
}

variable "jenkins_bitbucket_public_key_secret" {
  description = "Name of the AWS Secrets Manager secret to store Jenkins SSH public key"
  type        = string
}

variable "jenkins_agent_private_key_secret" {
  description = "Name of the secret for storing the Jenkins agent SSH private key"
  type        = string
  default     = "jenkins-agent-ssh-private-key"
}

variable "jenkins_agent_public_key_secret" {
  description = "Name of the secret for storing the Jenkins agent SSH public key"
  type        = string
  default     = "jenkins-agent-ssh-public-key"
}
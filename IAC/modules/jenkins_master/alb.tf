# Application Load Balancer for Jenkins
resource "aws_lb" "jenkins" {
  name               = "${var.name_prefix}-jenkins-lb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.jenkins_lb.id]
  subnets            = var.public_subnet_ids

  enable_deletion_protection = var.enable_deletion_protection

  tags = merge(
  {
    Name = "${var.name_prefix}-jenkins-lb"
    SubComponent = "application-load-balancer"
  },
  var.tags
)
}

# Target Group for the Load Balancer
resource "aws_lb_target_group" "jenkins" {
  name     = "${var.name_prefix}-jenkins-tg"
  port     = 8080
  protocol = "HTTP"
  vpc_id   = var.vpc_id
  
  health_check {
    path                = "/"
    interval            = 30
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 5  # Increased to allow more time for <PERSON> to initialize
    matcher             = "200-403"  # 403 might occur during initialization or if not logged in
  }

  tags = var.tags
}

# Attach the ASG to the target group
resource "aws_autoscaling_attachment" "jenkins" {
  autoscaling_group_name = aws_autoscaling_group.jenkins.name
  lb_target_group_arn    = aws_lb_target_group.jenkins.arn
}

# HTTP Listener - Redirects to HTTPS
resource "aws_lb_listener" "jenkins_http" {
  load_balancer_arn = aws_lb.jenkins.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  tags = var.tags
}

# HTTPS Listener
resource "aws_lb_listener" "jenkins_https" {
  load_balancer_arn = aws_lb.jenkins.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = var.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.jenkins.arn
  }

  tags = var.tags
}

# Security group for the ALB
resource "aws_security_group" "jenkins_lb" {
  name        = "${var.name_prefix}-jenkins-lb-sg"
  description = "Security group for Jenkins load balancer"
  vpc_id      = var.vpc_id

  # Egress rule to allow traffic to Jenkins instances
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    {
      Name = "${var.name_prefix}-jenkins-lb-sg"
    },
    var.tags
  )
}

# Bitbucket IP addresses - HTTP
resource "aws_security_group_rule" "jenkins_bitbucket_webhook_http" {
  type              = "ingress"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  security_group_id = aws_security_group.jenkins_lb.id
  cidr_blocks       = var.bitbucket_cidrs
  description = "Allow BitBucket webhook connections"
}

# Bitbucket IP addresses - HTTPS
resource "aws_security_group_rule" "jenkins_bitbucket_webhook_https" {
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  security_group_id = aws_security_group.jenkins_lb.id
  cidr_blocks       = var.bitbucket_cidrs
  description = "Allow BitBucket webhook connections over HTTPS"
}

# Optional: Add office IP addresses if needed
resource "aws_security_group_rule" "jenkins_office_access" {
  count             = length(var.office_cidrs) > 0 ? 1 : 0
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  security_group_id = aws_security_group.jenkins_lb.id
  cidr_blocks       = var.office_cidrs
  description       = "Allow office IP access"
}

# Enable Jenkins instances to receive traffic from the ALB
resource "aws_security_group_rule" "jenkins_from_lb" {
  type                     = "ingress"
  from_port                = 8080
  to_port                  = 8080
  protocol                 = "tcp"
  security_group_id        = aws_security_group.jenkins.id
  source_security_group_id = aws_security_group.jenkins_lb.id
  description              = "Allow traffic from ALB to Jenkins"
}

/*resource "aws_security_group_rule" "jenkins_from_eksnodes" {
  type                     = "ingress"
  to_port                  = 8080
  protocol                 = "tcp"
  description              = "Allow traffic from EKS nodes  to Jenkins for sonarqube webhook"
}*/
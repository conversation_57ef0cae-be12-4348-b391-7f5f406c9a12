# Launch Template for <PERSON>
resource "aws_launch_template" "jenkins" {
  name_prefix   = "${var.name_prefix}-jen<PERSON>-"
  image_id      = data.aws_ami.ubuntu.id
  instance_type = var.instance_type

  # Root volume for OS and Jenkins installation
  block_device_mappings {
    device_name = "/dev/sda1"
    ebs {
      volume_size           = var.root_volume_size
      volume_type           = "gp3"
      encrypted             = true
      delete_on_termination = true
    }
  }

  # Attach the IAM instance profile
  iam_instance_profile {
    name = aws_iam_instance_profile.jenkins.name
  }

  # Network configuration - instances in private subnets
  network_interfaces {
    associate_public_ip_address = false
    security_groups             = [aws_security_group.jenkins.id]
  }

  # User data script for instance configuration
  user_data = base64encode(templatefile("${path.module}/templates/user_data.sh", {
    aws_region            = var.aws_region
    tailscale_secret_name = var.jenkins_tailscale_secret_name
    jenkins_admin_secret_name = var.jenkins_admin_secret_name
    script_version =  var.master_userdata_script_version
    monitoring_jmx_port = var.monitoring_jmx_port
    monitoring_jmx_prometheus_port = var.monitoring_jmx_prometheus_port
    name_prefix = var.name_prefix
    jenkins_private_key_secret = var.jenkins_bitbucket_private_key_secret
    jenkins_public_key_secret  = var.jenkins_bitbucket_public_key_secret
    jenkins_agent_private_key_secret = aws_secretsmanager_secret.jenkins_agent_private_key.name
   jenkins_agent_public_key_secret = aws_secretsmanager_secret.jenkins_agent_public_key.name
  }))

  # Tags for EC2 instances
tag_specifications {
  resource_type = "instance"
  tags = merge(
    {
      Name = "${var.name_prefix}-jenkins"
      SubComponent = "master-ec2-instance"
    },
    var.tags
  )
}

  # Tags for EBS volumes
  tag_specifications {
    resource_type = "volume"
    tags = merge(
      {
        Name = "${var.name_prefix}-jenkins-volume"
        Backup = "true"
      },
      var.tags
    )
  }

  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"  # Enforce IMDSv2
    http_put_response_hop_limit = 1
  }

  lifecycle {
    create_before_destroy = true
  }
}
output "asg_name" {
  description = "Name of the Jenkins Auto Scaling Group"
  value       = aws_autoscaling_group.jenkins.name
}

output "launch_template_id" {
  description = "ID of the Jenkins Launch Template"
  value       = aws_launch_template.jenkins.id
}

output "security_group_id" {
  description = "ID of the Jenkins Security Group"
  value       = aws_security_group.jenkins.id
}

output "iam_role_name" {
  description = "Name of the Jenkins IAM Role"
  value       = aws_iam_role.jenkins.name
}

output "backup_vault_name" {
  description = "Name of the AWS Backup vault"
  value       = aws_backup_vault.jenkins.name
}

output "backup_plan_id" {
  description = "ID of the AWS Backup plan"
  value       = aws_backup_plan.jenkins.id
}

output "alb_dns_name" {
  description = "DNS name of the Application Load Balancer"
  value       = aws_lb.jenkins.dns_name
}

output "alb_zone_id" {
  description = "Zone ID of the Application Load Balancer"
  value       = aws_lb.jenkins.zone_id
}

output "alb_arn" {
  description = "ARN of the Application Load Balancer"
  value       = aws_lb.jenkins.arn
}

# Output the key fingerprint for reference
output "ssh_key_fingerprint" {
  value       = tls_private_key.jenkins_ssh.public_key_fingerprint_md5
  description = "The fingerprint of the generated SSH key"
  sensitive   = true
}

output "jenkins_agent_private_key_secret_name" {
  description = "Name of the secret containing the Jenkins agent SSH private key"
  value       = aws_secretsmanager_secret.jenkins_agent_private_key.name
}

output "jenkins_agent_public_key_secret_name" {
  description = "Name of the secret containing the Jenkins agent SSH public key"
  value       = aws_secretsmanager_secret.jenkins_agent_public_key.name
}

# Export the actual public key for the agent module to use
output "jenkins_agent_public_key" {
  description = "Public key content for Jenkins agent SSH authentication"
  value       = tls_private_key.jenkins_agent_ssh.public_key_openssh
  sensitive   = false  # Public key can be exposed
}

# Add this to the existing outputs.tf file

output "jenkins_bitbucket_private_key" {
  description = "BitBucket private key for SSH authentication"
  value       = aws_secretsmanager_secret_version.jenkins_private_key.secret_string
  sensitive   = true
}
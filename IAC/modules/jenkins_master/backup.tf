# AWS Backup plan for <PERSON> data
resource "aws_backup_plan" "jenkins" {
  name = "${var.name_prefix}-jenkins-backup"

  rule {
    rule_name         = "frequent_backup"
    target_vault_name = aws_backup_vault.jenkins.name
    schedule          = "cron(0 1 * * ? *)"  # Daily at 1 AM UTC
    
    lifecycle {
      delete_after = var.backup_retention_days
    }
  }

  tags = merge(
    {
      SubComponent = "backup-plan"
    },
    var.tags
  )
}

# Backup vault for storing Jenkins backups
resource "aws_backup_vault" "jenkins" {
  name = "${var.name_prefix}-jenkins-vault"
  tags = merge(
    {
      SubComponent = "backup-vault"
    },
    var.tags
  )
}

# Selection of resources to backup
resource "aws_backup_selection" "jenkins" {
  name         = "${var.name_prefix}-jenkins-backup-selection"
  plan_id      = aws_backup_plan.jenkins.id
  iam_role_arn = aws_iam_role.backup.arn

  selection_tag {
    type  = "STRINGEQUALS"
    key   = "Backup"
    value = "true"
  }
}
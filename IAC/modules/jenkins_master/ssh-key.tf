# Add to modules/jenkins_master/ssh_key.tf

# Generate an RSA key pair for Jenkins
resource "tls_private_key" "jenkins_ssh" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

# Store the private key in Secrets Manager
resource "aws_secretsmanager_secret" "jenkins_private_key" {
  name        = "${var.name_prefix}-jenkins-ssh-private-key"
  description = "Jenkins SSH private key for authentication"
  tags = merge(
    {
      SubComponent = "credential-storage"
    },
    var.tags
  )
}

resource "aws_secretsmanager_secret_version" "jenkins_private_key" {
  secret_id     = aws_secretsmanager_secret.jenkins_private_key.id
  secret_string = tls_private_key.jenkins_ssh.private_key_pem
}

# Store the public key in Secrets Manager
resource "aws_secretsmanager_secret" "jenkins_public_key" {
  name        = "${var.name_prefix}-jenkins-ssh-public-key"
  description = "Jenkins SSH public key for authentication"
  tags = merge(
    {
      SubComponent = "credential-storage"
    },
    var.tags
  )
}

resource "aws_secretsmanager_secret_version" "jenkins_public_key" {
  secret_id     = aws_secretsmanager_secret.jenkins_public_key.id
  secret_string = tls_private_key.jenkins_ssh.public_key_openssh
}



# Generate an RSA key pair specifically for <PERSON> agents
resource "tls_private_key" "jenkins_agent_ssh" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

# Store the agent private key in Secrets Manager
resource "aws_secretsmanager_secret" "jenkins_agent_private_key" {
  name        = "${var.name_prefix}-jenkins-agent-ssh-private-key"
  description = "Jenkins agent SSH private key for master-agent authentication"
  tags = merge(
    {
      SubComponent = "credential-storage"
    },
    var.tags
  )
}

resource "aws_secretsmanager_secret_version" "jenkins_agent_private_key" {
  secret_id     = aws_secretsmanager_secret.jenkins_agent_private_key.id
  secret_string = tls_private_key.jenkins_agent_ssh.private_key_pem
}

# Store the agent public key in Secrets Manager
resource "aws_secretsmanager_secret" "jenkins_agent_public_key" {
  name        = "${var.name_prefix}-jenkins-agent-ssh-public-key"
  description = "Jenkins agent SSH public key for master-agent authentication"
  tags = merge(
    {
      SubComponent = "credential-storage"
    },
    var.tags
  )
}

resource "aws_secretsmanager_secret_version" "jenkins_agent_public_key" {
  secret_id     = aws_secretsmanager_secret.jenkins_agent_public_key.id
  secret_string = tls_private_key.jenkins_agent_ssh.public_key_openssh
}
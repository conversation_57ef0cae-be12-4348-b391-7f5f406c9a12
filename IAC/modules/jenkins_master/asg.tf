# Auto Scaling Group for Jenkins (min 1, max 1)
resource "aws_autoscaling_group" "jenkins" {
  name                = "${var.name_prefix}-jenkins-asg"
  min_size            = 1
  max_size            = 1
  desired_capacity    = 1
  vpc_zone_identifier = var.subnet_ids
  health_check_type   = "EC2"
  health_check_grace_period = 600  # 10 minutes to allow <PERSON> to start up

  launch_template {
    id      = aws_launch_template.jenkins.id
    version = "$Latest"
  }

  # Instance refresh policy for controlled updates
  instance_refresh {
    strategy = "Rolling"
    preferences {
      min_healthy_percentage = 0
    }
  }

  dynamic "tag" {
    for_each = merge(
      {
        Name = "${var.name_prefix}-jenkins"
      },
      var.tags
    )
    content {
      key                 = tag.key
      value               = tag.value
      propagate_at_launch = true
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Optional: Create ASG scheduled actions to automatically stop/start Jenkins
# Uncomment to enable cost optimization for non-production environments

/*
resource "aws_autoscaling_schedule" "scale_down" {
  scheduled_action_name  = "${var.name_prefix}-jenkins-scale-down"
  min_size               = 0
  max_size               = 0
  desired_capacity       = 0
  recurrence             = "0 20 * * 1-5"  # 8 PM Mon-Fri
  time_zone              = "America/New_York"
  autoscaling_group_name = aws_autoscaling_group.jenkins.name
}

resource "aws_autoscaling_schedule" "scale_up" {
  scheduled_action_name  = "${var.name_prefix}-jenkins-scale-up"
  min_size               = 1
  max_size               = 1
  desired_capacity       = 1
  recurrence             = "0 8 * * 1-5"  # 8 AM Mon-Fri
  time_zone              = "America/New_York"
  autoscaling_group_name = aws_autoscaling_group.jenkins.name
}
*/
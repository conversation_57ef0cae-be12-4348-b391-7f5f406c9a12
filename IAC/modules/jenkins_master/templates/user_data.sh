#!/bin/bash
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}
log_message "script version running: ${script_version}"
log_message "Updating and installing packages"
sudo apt-get update && sudo apt-get upgrade -y && sudo apt-get install -y curl jq unzip git openssh-client openjdk-17-jdk
sudo snap install aws-cli --classic
# Install CloudWatch Agent
log_message "Installing CloudWatch Agent..."
wget https://amazoncloudwatch-agent.s3.amazonaws.com/ubuntu/amd64/latest/amazon-cloudwatch-agent.deb
sudo dpkg -i amazon-cloudwatch-agent.deb
# Create CloudWatch Agent configuration with only essential metrics
log_message "Configuring CloudWatch Agent..."
sudo mkdir -p /opt/aws/cloudwatch/config
cat > /tmp/cloudwatch-config.json << 'EOL'
{
  "agent": {
    "metrics_collection_interval": 60,
    "run_as_user": "root"
  },
  "metrics": {
    "namespace": "JenkinsMetrics",
    "metrics_collected": {
      "cpu": {
        "resources": [
          "*"
        ],
        "measurement": [
          "cpu_usage_user",
          "cpu_usage_system"
        ],
        "totalcpu": true
      },
      "mem": {
        "measurement": [
          "mem_used_percent",
          "mem_available"
        ]
      },
      "disk": {
        "resources": [
          "/"
        ],
        "measurement": [
          "used_percent",
          "disk_free"
        ]
      },
      "processes": {
        "measurement": [
          "running",
          "blocked"
        ]
      }
    },
    "append_dimensions": {
      "InstanceId": "$${aws:InstanceId}",
      "AutoScalingGroupName": "$${aws:AutoScalingGroupName}"
    }
  },
  "logs": {
    "logs_collected": {
      "files": {
        "collect_list": [
          {
            "file_path": "/var/log/jenkins/jenkins.log",
            "log_group_name": "${name_prefix}-jenkins-logs",
            "log_stream_name": "{instance_id}-jenkins-app",
            "retention_in_days": 14
          },
          {
            "file_path": "/var/log/syslog",
            "log_group_name": "${name_prefix}-jenkins-logs",
            "log_stream_name": "{instance_id}-syslog",
            "retention_in_days": 14
          }
        ]
      }
    }
  }
}
EOL
# Replace template variables
sed -i "s/\${name_prefix}/${name_prefix}/g" /tmp/cloudwatch-config.json
sudo mv /tmp/cloudwatch-config.json /opt/aws/cloudwatch/config/config.json

# Start CloudWatch Agent
log_message "Starting CloudWatch Agent..."
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -s -c file:/opt/aws/cloudwatch/config/config.json
# Install Jenkins-specific packages
log_message "Installing Jenkins-specific packages..."
# Download JMX exporter for Java applications (minimal config)
log_message "Setting up JMX monitoring..."
sudo mkdir -p /opt/jmx_exporter
sudo wget -O /opt/jmx_exporter/jmx_prometheus_javaagent.jar https://repo1.maven.org/maven2/io/prometheus/jmx/jmx_prometheus_javaagent/0.18.0/jmx_prometheus_javaagent-0.18.0.jar
# Create minimal JMX exporter config
cat > /tmp/jmx_config.yaml << 'EOL'
---
lowercaseOutputName: true
lowercaseOutputLabelNames: true
rules:
  # Essential Jenkins metrics only
  - pattern: "jenkins.executor.count.value"
    name: jenkins_executor_count
    help: "Number of executors"
    type: GAUGE
  - pattern: "jenkins.executor.in_use.value"
    name: jenkins_executor_in_use
    help: "Number of executors in use"
    type: GAUGE
  # Critical JVM memory metrics only
  - pattern: "java.lang<type=Memory><HeapMemoryUsage>used"
    name: jvm_memory_heap_used
    help: "JVM heap memory used"
    type: GAUGE
  - pattern: "java.lang<type=Memory><HeapMemoryUsage>max"
    name: jvm_memory_heap_max
    help: "JVM heap memory max"
    type: GAUGE
EOL
sudo mv /tmp/jmx_config.yaml /opt/jmx_exporter/config.yaml
# Jenkins Installation
JENKINS_KEY_PATH="/usr/share/keyrings/jenkins-keyring.asc"
if [ ! -f "$JENKINS_KEY_PATH" ]; then
    log_message "Adding Jenkins repository key..."
    curl -fsSL https://pkg.jenkins.io/debian-stable/jenkins.io-2023.key | sudo tee \
        "$JENKINS_KEY_PATH" > /dev/null
    log_message "Adding Jenkins repository..."
    echo deb [signed-by="$JENKINS_KEY_PATH"] \
        https://pkg.jenkins.io/debian-stable binary/ | sudo tee \
        /etc/apt/sources.list.d/jenkins.list > /dev/null
    sudo apt-get update
    log_message "Installing Jenkins..."
    sudo apt-get install -y jenkins
    # Configure Java options for Jenkins to include minimal JMX monitoring
    JENKINS_OPTS="JAVA_OPTS=\"\$JAVA_OPTS -Dcom.sun.management.jmxremote \
-Dcom.sun.management.jmxremote.port=${monitoring_jmx_port} \
-Dcom.sun.management.jmxremote.local.only=false \
-Dcom.sun.management.jmxremote.authenticate=false \
-Dcom.sun.management.jmxremote.ssl=false \
-Djava.rmi.server.hostname=localhost \
-javaagent:/opt/jmx_exporter/jmx_prometheus_javaagent.jar=${monitoring_jmx_prometheus_port}:/opt/jmx_exporter/config.yaml\""
    # Create Jenkins service override directory
    sudo mkdir -p /etc/systemd/system/jenkins.service.d/
    # Add custom configuration to override.conf
    echo "[Service]
Environment=$JENKINS_OPTS" | sudo tee /etc/systemd/system/jenkins.service.d/override.conf
    # Reload systemd and restart Jenkins
    sudo systemctl daemon-reload
    # Wait for Jenkins to start
    log_message "Starting Jenkins service..."
    sudo systemctl enable jenkins
    sudo systemctl start jenkins
else
    log_message "Jenkins repository already configured"
    # Ensure Jenkins is running
    if ! systemctl is-active --quiet jenkins; then
        log_message "Starting Jenkins service..."
        sudo systemctl enable jenkins
        sudo systemctl start jenkins
    else
        log_message "Jenkins service is already running"
    fi
fi
# Set up SSH for Jenkins
log_message "Setting up SSH for Jenkins..."
JENKINS_HOME="/var/lib/jenkins"
SSH_DIR="$${JENKINS_HOME}/.ssh"
# Create SSH directory with proper permissions
sudo -u jenkins mkdir -p $SSH_DIR
sudo chmod 700 $SSH_DIR
# Check if we should generate new keys or retrieve existing ones from Secrets Manager
# First, try to retrieve existing keys from Secrets Manager
log_message "Checking for existing SSH keys in Secrets Manager..."
PRIVATE_KEY=""
PUBLIC_KEY=""
# Check if keys already exist in Secrets Manager
if aws secretsmanager describe-secret --secret-id "${jenkins_private_key_secret}" --region "${aws_region}" &>/dev/null; then
    log_message "Found existing private key in Secrets Manager, retrieving..."
    PRIVATE_KEY=$(aws secretsmanager get-secret-value \
        --region "${aws_region}" \
        --secret-id "${jenkins_private_key_secret}" \
        --query SecretString \
        --output text 2>/dev/null)
fi
if aws secretsmanager describe-secret --secret-id "${jenkins_public_key_secret}" --region "${aws_region}" &>/dev/null; then
    log_message "Found existing public key in Secrets Manager, retrieving..."
    PUBLIC_KEY=$(aws secretsmanager get-secret-value \
        --region "${aws_region}" \
        --secret-id "${jenkins_public_key_secret}" \
        --query SecretString \
        --output text 2>/dev/null)
fi
# Write the keys to the Jenkins .ssh directory
log_message "Setting up SSH keys for Jenkins..."
echo "$PRIVATE_KEY" | sudo -u jenkins tee $SSH_DIR/id_rsa > /dev/null
echo "$PUBLIC_KEY" | sudo -u jenkins tee $SSH_DIR/id_rsa.pub > /dev/null
# Set correct permissions
sudo chmod 600 $SSH_DIR/id_rsa
sudo chmod 644 $SSH_DIR/id_rsa.pub
# Setup known_hosts file
log_message "Setting up known_hosts file for Jenkins..."
sudo -u jenkins touch $SSH_DIR/known_hosts
# Add common Git hosts to known_hosts
for HOST in "github.com" "gitlab.com" "bitbucket.org"; do
    log_message "Adding $HOST to known_hosts..."
    sudo -u jenkins ssh-keyscan -t rsa $HOST >> $SSH_DIR/known_hosts
done
# Set proper permissions for known_hosts
sudo chmod 644 $SSH_DIR/known_hosts
sudo chown -R jenkins:jenkins $SSH_DIR
# Set up SSH key for Jenkins agents
log_message "Setting up SSH keys for Jenkins agents..."
# Check if agent keys already exist in Secrets Manager
AGENT_PRIVATE_KEY=""
AGENT_PUBLIC_KEY=""
if aws secretsmanager describe-secret --secret-id "${jenkins_agent_private_key_secret}" --region "${aws_region}" &>/dev/null; then
    log_message "Found existing agent private key in Secrets Manager, retrieving..."
    AGENT_PRIVATE_KEY=$(aws secretsmanager get-secret-value \
        --region "${aws_region}" \
        --secret-id "${jenkins_agent_private_key_secret}" \
        --query SecretString \
        --output text 2>/dev/null)
fi
if aws secretsmanager describe-secret --secret-id "${jenkins_agent_public_key_secret}" --region "${aws_region}" &>/dev/null; then
    log_message "Found existing agent public key in Secrets Manager, retrieving..."
    AGENT_PUBLIC_KEY=$(aws secretsmanager get-secret-value \
        --region "${aws_region}" \
        --secret-id "${jenkins_agent_public_key_secret}" \
        --query SecretString \
        --output text 2>/dev/null)
fi
# Write the agent keys to the Jenkins .ssh directory (using different filenames)
log_message "Setting up SSH keys for Jenkins agents..."
echo "$AGENT_PRIVATE_KEY" | sudo -u jenkins tee $SSH_DIR/agent_rsa > /dev/null
echo "$AGENT_PUBLIC_KEY" | sudo -u jenkins tee $SSH_DIR/agent_rsa.pub > /dev/null
# Set correct permissions
sudo chmod 600 $SSH_DIR/agent_rsa
sudo chmod 644 $SSH_DIR/agent_rsa.pub
# Install Tailscale
log_message "Installing Tailscale..."
if ! command -v tailscale &>/dev/null; then
    curl -fsSL https://tailscale.com/install.sh | sh
    # Ensure Tailscale service is running
    sudo systemctl enable tailscaled
    sudo systemctl start tailscaled
    log_message "Tailscale installed successfully"
else
    log_message "Tailscale already installed"
fi
# Check if Tailscale is authenticated
TAILSCALE_STATUS=$(sudo tailscale status --json 2>/dev/null | jq -r '.BackendState' 2>/dev/null)
if [ "$TAILSCALE_STATUS" != "Running" ]; then
    log_message "Tailscale not authenticated. Attempting to authenticate..."  
    # Retrieve Tailscale auth key from Secrets Manager with retries
    MAX_RETRIES=5
    RETRY_COUNT=0
    TAILSCALE_KEY=""
    while [ -z "$TAILSCALE_KEY" ] && [ "$RETRY_COUNT" -lt "$MAX_RETRIES" ]; do
        log_message "Attempting to retrieve Tailscale auth key (attempt $((RETRY_COUNT + 1))/$MAX_RETRIES)..."
        TAILSCALE_KEY=$(aws secretsmanager get-secret-value \
            --region "${aws_region}" \
            --secret-id "${tailscale_secret_name}" \
            --query SecretString \
            --output text 2>/dev/null)
        if [ -z "$TAILSCALE_KEY" ]; then
            log_message "Failed to retrieve Tailscale auth key. Retrying in 10 seconds..."
            sleep 10
            RETRY_COUNT=$((RETRY_COUNT + 1))
        fi
    done
    if [ -z "$TAILSCALE_KEY" ]; then
        log_message "ERROR: Failed to retrieve Tailscale auth key after $MAX_RETRIES attempts."
        log_message "Verify IAM permissions and secret existence."
        log_message "Secret name: ${tailscale_secret_name}"
    else
        log_message "Successfully retrieved Tailscale auth key."
        # Attempt to authenticate Tailscale
        sudo tailscale up --auth-key="$TAILSCALE_KEY" --hostname="jenkins-personal-$(hostname)"
        if [ $? -eq 0 ]; then
            log_message "Successfully authenticated Tailscale!"
        else
            log_message "ERROR: Failed to authenticate Tailscale with the provided key."
        fi
    fi
else
    log_message "Tailscale is already authenticated and running."
fi
# Wait for Jenkins to initialize and get the admin password
log_message "Waiting for Jenkins to initialize..."
COUNTER=0
MAX_WAIT=180  # 15 minutes maximum wait time
while [ ! -f /var/lib/jenkins/secrets/initialAdminPassword ]; do
    sleep 5
    COUNTER=$((COUNTER + 1))
    if [ "$COUNTER" -ge "$MAX_WAIT" ]; then
        log_message "ERROR: Timeout waiting for Jenkins to initialize"
        log_message "Jenkins may still be starting. Check again later for the admin password at /var/lib/jenkins/secrets/initialAdminPassword"
        break
    fi
done
# If Jenkins initialized successfully, save admin password to Secrets Manager
if [ -f /var/lib/jenkins/secrets/initialAdminPassword ]; then
    log_message "Jenkins initialized. Retrieving admin password..."
    JENKINS_ADMIN_PASSWORD=$(sudo cat /var/lib/jenkins/secrets/initialAdminPassword)
    if [ -n "$JENKINS_ADMIN_PASSWORD" ]; then
        log_message "Admin password retrieved. Storing in Secrets Manager..."
        # Check if the secret already exists
        if aws secretsmanager describe-secret \
            --secret-id "${jenkins_admin_secret_name}" \
            --region "${aws_region}" &>/dev/null; then
            log_message "Secret exists, updating the value..."
            if aws secretsmanager put-secret-value \
                --secret-id "${jenkins_admin_secret_name}" \
                --secret-string "$JENKINS_ADMIN_PASSWORD" \
                --region "${aws_region}" &>/dev/null; then
                log_message "Successfully updated Jenkins admin password in Secrets Manager."
            else
                log_message "ERROR: Failed to update Jenkins admin password in Secrets Manager."
            fi
        else
            log_message "Secret doesn't exist, creating new secret..."
            if aws secretsmanager create-secret \
                --name "${jenkins_admin_secret_name}" \
                --description "Jenkins initial admin password" \
                --secret-string "$JENKINS_ADMIN_PASSWORD" \
                --region "${aws_region}" &>/dev/null; then
                log_message "Successfully created Jenkins admin password in Secrets Manager."
            else
                log_message "ERROR: Failed to create Jenkins admin password in Secrets Manager."
            fi
        fi
        # Print admin password for convenience during setup
        log_message "Jenkins admin password: $JENKINS_ADMIN_PASSWORD"
    else
        log_message "ERROR: Failed to retrieve Jenkins admin password"
    fi
else
    log_message "Jenkins initial admin password file not found. Jenkins may still be initializing."
fi
# Print summary information
log_message "Jenkins setup completed."
log_message "- Jenkins service status: $(systemctl is-active jenkins)"
log_message "- CloudWatch agent status: $(systemctl is-active amazon-cloudwatch-agent)"
log_message "- Tailscale service status: $(systemctl is-active tailscaled)"
log_message "- Tailscale connection status: $(sudo tailscale status --json | jq -r '.BackendState' 2>/dev/null || echo 'Unknown')"
if [ "$(sudo tailscale status --json | jq -r '.BackendState' 2>/dev/null)" = "Running" ]; then
    TAILSCALE_IP=$(sudo tailscale ip -4)
    log_message "Jenkins should be accessible via Tailscale at: http://$TAILSCALE_IP:8080"
else
    log_message "Tailscale not fully connected. You may need to run 'sudo tailscale up' manually."
fi
log_message "Setup script completed."
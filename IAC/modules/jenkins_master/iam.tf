# IAM role for Jenkins instance
resource "aws_iam_role" "jenkins" {
  name = "${var.name_prefix}-jenkins-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Instance profile for the IAM role
resource "aws_iam_instance_profile" "jenkins" {
  name = "${var.name_prefix}-jenkins-profile"
  role = aws_iam_role.jenkins.name
}

# Policy for Jenkins instance to access Secrets Manager
resource "aws_iam_policy" "jenkins_secrets" {
  name        = "${var.name_prefix}-jenkins-secrets-policy"
  description = "Policy for Jenkins to access Tailscale secret and store admin password"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:CreateSecret",
          "secretsmanager:PutSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Resource = [
          data.aws_secretsmanager_secret.tailscale.arn,
          aws_secretsmanager_secret.jenkins_private_key.arn,
          aws_secretsmanager_secret.jenkins_public_key.arn,
          aws_secretsmanager_secret.jenkins_agent_private_key.arn,
          aws_secretsmanager_secret.jenkins_agent_public_key.arn,
          "arn:aws:secretsmanager:${var.aws_region}:${var.account_id}:secret:${var.jenkins_admin_secret_name}*"
        ]
      }
    ]
  })
}

# Policy for Jenkins to work with EC2 and EBS resources
resource "aws_iam_policy" "jenkins_ec2" {
  name        = "${var.name_prefix}-jenkins-ec2-policy"
  description = "Policy for Jenkins to work with EC2 resources"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:DescribeVolumes",
          "ec2:DescribeSnapshots",
          "ec2:DescribeInstances",
          "ec2:DescribeTags",
          "ec2:DescribeKeyPairs",  # Add this permission
          "ec2:DescribeSubnets",   # Also helpful for EC2 plugin
          "ec2:DescribeSecurityGroups"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateVolume",
          "ec2:DeleteVolume"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:AttachVolume",
          "ec2:DetachVolume",
          "ec2:ModifyInstanceAttribute"
        ]
        Resource = [
          "arn:aws:ec2:${var.aws_region}:${var.account_id}:volume/*",
          "arn:aws:ec2:${var.aws_region}:${var.account_id}:instance/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateTags",
          "ec2:DeleteTags"
        ]
        Resource = "arn:aws:ec2:${var.aws_region}:${var.account_id}:*/*"
      }
    ]
  })
}

# Attach policies to the Jenkins role
resource "aws_iam_role_policy_attachment" "jenkins_secrets" {
  role       = aws_iam_role.jenkins.name
  policy_arn = aws_iam_policy.jenkins_secrets.arn
}

resource "aws_iam_role_policy_attachment" "jenkins_ec2" {
  role       = aws_iam_role.jenkins.name
  policy_arn = aws_iam_policy.jenkins_ec2.arn
}

# Attach SSM policy for Session Manager access
resource "aws_iam_role_policy_attachment" "jenkins_ssm" {
  role       = aws_iam_role.jenkins.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

# IAM role for AWS Backup
resource "aws_iam_role" "backup" {
  name = "${var.name_prefix}-jenkins-backup-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "backup.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Attach backup policy to the role
resource "aws_iam_role_policy_attachment" "backup" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup"
  role       = aws_iam_role.backup.name
}

# Policy for Jenkins to publish metrics to CloudWatch
resource "aws_iam_policy" "jenkins_cloudwatch" {
  name        = "${var.name_prefix}-jenkins-cloudwatch-policy"
  description = "Policy for Jenkins to publish metrics to CloudWatch"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:PutMetricData",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams",
          "logs:DescribeLogGroups",
          "logs:CreateLogStream",
          "logs:CreateLogGroup"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:DescribeTags"
        ],
        Resource = "*"
      }
    ]
  })
}

# Attach the CloudWatch policy to the Jenkins role
resource "aws_iam_role_policy_attachment" "jenkins_cloudwatch" {
  role       = aws_iam_role.jenkins.name
  policy_arn = aws_iam_policy.jenkins_cloudwatch.arn
}

# Policy for Jenkins to manage EC2 Fleet instances (based on official plugin documentation)
resource "aws_iam_policy" "jenkins_ec2_fleet" {
  name        = "${var.name_prefix}-jenkins-ec2-fleet-policy"
  description = "Policy for Jenkins to manage EC2 Fleet, Spot Fleet, and Auto Scaling Groups"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:DescribeSpotFleetInstances",
          "ec2:ModifySpotFleetRequest",
          "ec2:CreateTags",
          "ec2:DescribeRegions",
          "ec2:DescribeInstances",
          "ec2:TerminateInstances",
          "ec2:DescribeInstanceStatus",
          "ec2:DescribeSpotFleetRequests",
          "ec2:DescribeFleets",
          "ec2:DescribeFleetInstances",
          "ec2:ModifyFleet",
          "ec2:DescribeInstanceTypes"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "autoscaling:DescribeAutoScalingGroups",
          "autoscaling:UpdateAutoScalingGroup"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "iam:ListInstanceProfiles",
          "iam:ListRoles"
        ]
        Resource = ["*"]
      },
      {
        Effect = "Allow"
        Action = ["iam:PassRole"]
        Resource = ["*"]
        Condition = {
          StringEquals = {
            "iam:PassedToService" = [
              "ec2.amazonaws.com",
              "ec2.amazonaws.com.cn"
            ]
          }
        }
      }
    ]
  })
}

# Attach the EC2 Fleet policy to the Jenkins role
resource "aws_iam_role_policy_attachment" "jenkins_ec2_fleet" {
  role       = aws_iam_role.jenkins.name
  policy_arn = aws_iam_policy.jenkins_ec2_fleet.arn
}
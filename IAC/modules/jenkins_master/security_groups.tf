# Security group for Jenkins instance
resource "aws_security_group" "jenkins" {
  name        = "${var.name_prefix}-jenkins-sg"
  description = "Security group for Jenkins server"
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

tags = merge(
  {
    Name = "${var.name_prefix}-jenkins-sg"
    SubComponent = "security-group"
  },
  var.tags
)
}

# Optional: Define specific inbound rules for Jenkins
# Uncomment and modify as needed

/*
resource "aws_security_group_rule" "jenkins_http" {
  type              = "ingress"
  from_port         = 8080
  to_port           = 8080
  protocol          = "tcp"
  security_group_id = aws_security_group.jenkins.id
  # Consider limiting this to specific sources
  cidr_blocks       = ["10.0.0.0/8"]
  description       = "Allow Jenkins HTTP"
}

resource "aws_security_group_rule" "jenkins_https" {
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  security_group_id = aws_security_group.jenkins.id
  cidr_blocks       = ["10.0.0.0/8"]
  description       = "Allow Jenkins HTTPS"
}
*/
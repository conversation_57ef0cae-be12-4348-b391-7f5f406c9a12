# CloudWatch Dashboard for <PERSON> with essential metrics only
resource "aws_cloudwatch_dashboard" "jenkins" {
  dashboard_name = "${var.name_prefix}-jenkins-dashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["JenkinsMetrics", "cpu_usage_user", "AutoScalingGroupName", aws_autoscaling_group.jenkins.name, { "stat" = "Average" }],
            ["JenkinsMetrics", "cpu_usage_system", "AutoScalingGroupName", aws_autoscaling_group.jenkins.name, { "stat" = "Average" }]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "CPU Usage"
          period  = 300
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["JenkinsMetrics", "mem_used_percent", "AutoScalingGroupName", aws_autoscaling_group.jenkins.name, { "stat" = "Average" }]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "Memory Used %"
          period  = 300
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["JenkinsMetrics", "jvm_memory_heap_used", "AutoScalingGroupName", aws_autoscaling_group.jenkins.name, { "stat" = "Average" }],
            ["JenkinsMetrics", "jvm_memory_heap_max", "AutoScalingGroupName", aws_autoscaling_group.jenkins.name, { "stat" = "Average" }]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "JVM Heap Memory"
          period  = 300
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["JenkinsMetrics", "disk_used_percent", "AutoScalingGroupName", aws_autoscaling_group.jenkins.name, "path", "/", { "stat" = "Average" }]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "Disk Usage %"
          period  = 300
        }
      }
    ]
  })
}

# Critical CloudWatch Alarms only
resource "aws_cloudwatch_metric_alarm" "jenkins_memory_critical" {
  alarm_name          = "${var.name_prefix}-jenkins-memory-critical"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "mem_used_percent"
  namespace           = "JenkinsMetrics"
  period              = 300
  statistic           = "Average"
  threshold           = 90
  alarm_description   = "Critical: Jenkins memory usage is too high"
  alarm_actions       = [aws_sns_topic.jenkins_alerts.arn]
  ok_actions          = [aws_sns_topic.jenkins_alerts.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.jenkins.name
  }
  tags = merge(
    {
      SubComponent = "monitoring-alarm"
    },
    var.tags
  )
}

resource "aws_cloudwatch_metric_alarm" "jenkins_disk_critical" {
  alarm_name          = "${var.name_prefix}-jenkins-disk-critical"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "disk_used_percent"
  namespace           = "JenkinsMetrics"
  period              = 300
  statistic           = "Average"
  threshold           = 85
  alarm_description   = "Critical: Jenkins disk usage is too high"
  alarm_actions       = [aws_sns_topic.jenkins_alerts.arn]
  ok_actions          = [aws_sns_topic.jenkins_alerts.arn]
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.jenkins.name
    path                 = "/"
  }
}

# SNS Topic for alerts
resource "aws_sns_topic" "jenkins_alerts" {
  name = "${var.name_prefix}-jenkins-alerts"
  tags = merge(
    {
      SubComponent = "notification-service"
    },
    var.tags
  )
}

# Optional: Add email subscription for alerts
resource "aws_sns_topic_subscription" "jenkins_alerts_email" {
  count     = length(var.alert_emails) > 0 ? length(var.alert_emails) : 0
  topic_arn = aws_sns_topic.jenkins_alerts.arn
  protocol  = "email"
  endpoint  = var.alert_emails[count.index]
}
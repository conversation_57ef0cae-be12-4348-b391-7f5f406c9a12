# Get the latest Ubuntu 24.04 AMI
data "aws_ami" "ubuntu" {
  most_recent = true
  
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*"]
  }
  
  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
  
  # Canonical's AWS account ID
  owners = ["************"]
}

# Fetch the Tailscale authentication key from AWS Secrets Manager
data "aws_secretsmanager_secret" "tailscale" {
  name = var.jenkins_tailscale_secret_name
}

#IAC/modules/eks-addons/ebs-csi-driver/variables.tf
variable "resource_requests_cpu" {
  description = "CPU resource requests for SonarQube"
  type        = string
  default     = "50m"
}

variable "resource_requests_memory" {
  description = "Memory resource requests for SonarQube"
  type        = string
  default     = "100Mi"
}

variable "resource_limits_cpu" {
  description = "CPU resource limits for SonarQube"
  type        = string
  default     = "100m"
}

variable "resource_limits_memory" {
  description = "Memory resource limits for SonarQube"
  type        = string
  default     = "200Mi"
}

variable "cluster_name" {
  type        = string
  description = "Name of the EKS cluster"
}

variable "replicas_count" {
  description = "number of replicas"
  type = number
  default = 1
}

variable "chart_version" {
  description = "chart version"
  type = string
  default = "2.40.3"
}

variable "oidc_provider_arn" {
  description = "The ARN of the EKS OIDC Provider"
  type        = string
}
#IAC/modules/eks-addons/ebs-csi-driver/main.tf
# Configure helm to connect to the EKS cluster
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

locals {
  service-account-name = "aws-ebs-csi-driver"
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority.0.data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

module "ebs-csi-driver_irsa_role" {
  source = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"

  role_name             = "${var.cluster_name}-ebs-csi-driver"
  attach_ebs_csi_policy = true

  oidc_providers = {
    ex = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["kube-system:${local.service-account-name}"]
    }
  }
}

resource "helm_release" "ebs-csi-driver" {
  repository = "https://kubernetes-sigs.github.io/aws-ebs-csi-driver"
  chart      = "aws-ebs-csi-driver"
  name       = "aws-ebs-csi-driver"
  namespace  = "kube-system"
  version    = var.chart_version
  values = [templatefile("${path.module}/aws-ebs-csi-driver.yaml", {
    service_account_name : local.service-account-name
    iam_role_arn : module.ebs-csi-driver_irsa_role.iam_role_arn
    resource_requests_cpu : var.resource_requests_cpu
    resource_requests_memory : var.resource_requests_memory
    resource_limits_cpu : var.resource_limits_cpu
    resource_limits_memory : var.resource_limits_memory
    replicas_count : var.replicas_count
  })]
  timeout = 300
}


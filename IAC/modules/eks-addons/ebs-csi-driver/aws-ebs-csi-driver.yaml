#IAC/modules/eks-addons/ebs-csi-driver/aws-ebs-csi-driver.yaml
controller:
  replicaCount: ${replicas_count}
  resources:
    requests:
      cpu: "${resource_requests_cpu}"
      memory: "${resource_requests_memory}"
    limits:
      cpu: "${resource_limits_cpu}"
      memory: "${resource_limits_memory}"
  serviceAccount:
    name: ${service_account_name}
    annotations:
      eks.amazonaws.com/role-arn: ${iam_role_arn}

#IAC/modules/eks-addons/istio/istiod_values.yaml
# Global settings
global:
  logging:
    level: "default:info"
  proxy:
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi

# Pilot (Istiod) specific settings
pilot:
  autoscaleEnabled: true
  autoscaleMin: 1
  autoscaleMax: 3
  replicaCount: 1
  resources:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 512Mi
  env:
    PILOT_ENABLE_AMBIENT_CONTROLLERS: "true"
    PILOT_ENABLE_WAYPOINT_CONTROLLER: "true"

meshConfig:
  # Enable access logging
  accessLogFile: "/dev/stdout"
  # Default is permissive for easier onboarding
  outboundTrafficPolicy:
    mode: ALLOW_ANY
#IAC/modules/eks-addons/istio/main.tf
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

resource "kubernetes_namespace" "istio_system" {
  metadata {
    name = "istio-system"
  }
}

resource "kubernetes_namespace" "istio_ingress" {
  metadata {
    name = "istio-ingress"
  }
}

resource "helm_release" "istio_base" {
  name       = "istio-base"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "base"
  version    = var.istio_version
  namespace  = kubernetes_namespace.istio_system.metadata[0].name
}

# Install Kubernetes Gateway API CRDs
resource "null_resource" "install_gateway_api_crds" {
  provisioner "local-exec" {
    command = <<EOT
      kubectl get crd gateways.gateway.networking.k8s.io &> /dev/null || \
        { kubectl apply -f https://github.com/kubernetes-sigs/gateway-api/releases/download/v1.2.1/standard-install.yaml; }
    EOT
  }

  depends_on = [kubernetes_namespace.istio_system] 
}

resource "helm_release" "prometheus" {
  name       = "prometheus"
  repository = "https://prometheus-community.github.io/helm-charts"
  chart      = "prometheus"
  version    = "26.0"  # You can specify your preferred version
  namespace  = kubernetes_namespace.istio_system.metadata[0].name

  set {
    name  = "server.persistentVolume.enabled"
    value = "false"  # For simplicity; enable with proper storage for production
  }
  set {
    name = "alertmanager.enabled"
    value = "false" # For simplicity; enable with proper storage for production
  }

  depends_on = [
    helm_release.istiod
  ]
}


resource "helm_release" "kiali" {
  name       = "kiali"
  repository = "https://kiali.org/helm-charts"
  chart      = "kiali-server"
  version    = "v2.5"  # Compatible with Istio 1.24.x
  namespace  = kubernetes_namespace.istio_system.metadata[0].name
  
  values = [
    <<-EOF
    auth:
      strategy: anonymous
      
    deployment:
      logger:
        log_level: debug
      pod_labels:
        sidecar.istio.io/inject: "false"
        
    external_services:
      prometheus:
        url: "http://prometheus-server.istio-system.svc:80"
      grafana:
        enabled: false
      tracing:
        enabled: false
        
    istio_namespace: istio-system
    EOF
  ]

  depends_on = [
    helm_release.prometheus
  ]
}


resource "helm_release" "istiod" {
  name       = "istiod"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "istiod"
  version    = var.istio_version 
  namespace  = kubernetes_namespace.istio_system.metadata[0].name

  set {
    name  = "profile"
    value = "ambient"
  }

values = [file("${path.module}/istiod_values.yaml")]
  depends_on = [
    helm_release.istio_base,
    null_resource.install_gateway_api_crds, 
  ]
}

resource "helm_release" "istio_cni" {
  name       = "istio-cni"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "cni"
  version    = var.istio_version 
  namespace  = kubernetes_namespace.istio_system.metadata[0].name

  set {
    name  = "profile"
    value = "ambient"
  }

  depends_on = [helm_release.istiod]
}

resource "helm_release" "ztunnel" {
  name       = "ztunnel"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "ztunnel"
  version    = var.istio_version
  namespace  = kubernetes_namespace.istio_system.metadata[0].name
  values = [
    <<-EOF
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 300m
        memory: 512Mi
    EOF
  ]
  depends_on = [helm_release.istio_cni]
}


resource "helm_release" "istio_ingressgateway" {
  name       = "istio-ingressgateway"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "gateway"
  version    = var.istio_version 
  namespace  = kubernetes_namespace.istio_ingress.metadata[0].name

  values = [
    <<-EOF
    service:
      type: ClusterIP
      ports:
      - name: status-port
        port: 15021
        targetPort: 15021
      - name: http2
        port: 80
        targetPort: 8080
      - name: https
        port: 443
        targetPort: 8443
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
    EOF
  ]

  depends_on = [helm_release.istiod]
}

resource "kubectl_manifest" "shared_gateway" {
  yaml_body = templatefile("${path.module}/shared-gateway.yaml", {
    domain_name = var.domain_name
  })

  depends_on = [
    helm_release.istio_ingressgateway
  ]
}

resource "kubectl_manifest" "istio_alb_ingress" {
  yaml_body = templatefile("${path.module}/istio-alb-ingress.yaml", {
    certificate_arn = var.acm_certificate_arn,
    domain_name = var.domain_name
  })

  depends_on = [
    helm_release.istio_ingressgateway
  ]
}
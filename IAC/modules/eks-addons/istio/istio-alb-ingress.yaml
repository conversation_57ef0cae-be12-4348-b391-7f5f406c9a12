#IAC/modules/eks-addons/istio/istio-alb-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: istio-alb-ingress
  namespace: istio-ingress
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/healthcheck-path: /healthz/ready
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-port: "15021"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/certificate-arn: ${certificate_arn}
    # Auth0 integration annotations
    alb.ingress.kubernetes.io/load-balancer-attributes: "routing.http.preserve_host_header.enabled=true"
    # Add additional Auth0 annotations as needed
spec:
  rules:
    - host: "*.${domain_name}"
      http:
        paths:
          - path: /healthz/ready
            pathType: Prefix
            backend:
              service:
                name: shared-gateway-istio
                port:
                  number: 15021
          - path: /
            pathType: Prefix
            backend:
              service:
                name: shared-gateway-istio
                port:
                  number: 80
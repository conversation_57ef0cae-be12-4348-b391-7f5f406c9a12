#IAC/modules/eks-addons/istio/outputs.tf
output "istio_ingress_namespace" {
  description = "Namespace where the Istio ingress gateway is deployed"
  value       = kubernetes_namespace.istio_ingress.metadata[0].name
}

output "istio_system_namespace" {
  description = "Namespace where the Istio control plane is deployed"
  value       = kubernetes_namespace.istio_system.metadata[0].name
}

output "shared_gateway_name" {
  description = "Name of the shared Istio gateway"
  value       = "shared-gateway"
}
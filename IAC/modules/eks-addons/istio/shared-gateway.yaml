#IAC/modules/eks-addons/istio/shared-gateway.yaml
apiVersion: gateway.networking.k8s.io/v1beta1
kind: Gateway
metadata:
  name: shared-gateway
  namespace: istio-ingress
  annotations:
    # This annotation binds this Gateway to the specific gateway deployment
    # and prevents creating a new load balancer
    networking.istio.io/service-type: "ClusterIP"
    # Explicitly indicate which deployment should implement this Gateway
    istio.io/gateway-name: "istio-ingressgateway"
spec:
  gatewayClassName: istio
  listeners:
  - name: http
    port: 80
    protocol: HTTP
    hostname: "*.${domain_name}"
    allowedRoutes:
      namespaces:
        from: All
  - name: https
    port: 443
    protocol: HTTPS
    hostname: "*.${domain_name}"
    allowedRoutes:
      namespaces:
        from: All
#IAC/modules/eks-addons/loadbalancer-controller/main.tf
# Configure helm to connect to the EKS cluster
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

locals {
  lb-service-account-name = "aws-load-balancer-controller"
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority.0.data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

# # Load Balancer Controller
module "load_balancer_controller_irsa_role" {
  source = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"

  role_name                              = "${var.cluster_name}-load-balancer-controller"
  attach_load_balancer_controller_policy = true

  oidc_providers = {
    ex = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["kube-system:${local.lb-service-account-name}"]
    }
  }
}

resource "helm_release" "lb-controller" {
  repository = "https://aws.github.io/eks-charts"
  chart      = "aws-load-balancer-controller"
  name       = "aws-load-balancer-controller"
  namespace  = "kube-system"
  values = [templatefile("${path.module}/loadbalancer-controller.yaml", {
    cluster_name : var.cluster_name,
    service_account_name : local.lb-service-account-name
    iam_role_arn : module.load_balancer_controller_irsa_role.iam_role_arn
  })]
  timeout = 300
}


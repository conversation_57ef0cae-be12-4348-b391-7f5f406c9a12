# IAC/modules/eks-addons/argocd/outputs.tf

output "argocd_namespace" {
  description = "The Kubernetes namespace where ArgoCD is deployed"
  value       = kubernetes_namespace.argocd.metadata[0].name
}

output "argocd_server_service" {
  description = "Name of the ArgoCD server Kubernetes service"
  value       = "argocd-server"
}

output "argocd_admin_password_secret_arn" {
  description = "ARN of the AWS Secrets Manager secret containing the admin password"
  value       = aws_secretsmanager_secret.admin_password.arn
}

output "argocd_admin_password_secret_name" {
  description = "Name of the AWS Secrets Manager secret containing the admin password"
  value       = aws_secretsmanager_secret.admin_password.name
}

output "argocd_url" {
  description = "URL to access ArgoCD"
  value       = var.argocd_hostname != "" ? "https://${var.argocd_hostname}" : "Use port-forwarding to access ArgoCD"
}


output "argocd_bitbucket_private_key_secret_arn" {
  description = "ARN of the AWS Secrets Manager secret containing the Bitbucket SSH private key"
  value       = aws_secretsmanager_secret.argocd_bitbucket_private_key.arn
}

output "argocd_bitbucket_public_key" {
  description = "The public key to add to Bitbucket for SSH authentication"
  value       = tls_private_key.argocd_bitbucket_ssh.public_key_openssh
  sensitive   = false  # Public key can be exposed
}

output "argocd_bitbucket_public_key_secret_name" {
  description = "Name of the AWS Secrets Manager secret containing the Bitbucket SSH public key"
  value       = aws_secretsmanager_secret.argocd_bitbucket_public_key.name
}
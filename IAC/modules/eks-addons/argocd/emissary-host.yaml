#IAC/modules/eks-addons/argocd/emissary-host.yaml
apiVersion: getambassador.io/v3alpha1
kind: Host
metadata:
  name: argocd-host
  namespace: ${emissary_namespace}
spec:
  hostname: ${argocd_hostname}
  acmeProvider:
    authority: none
  # Tell Emissary to trust the X-Forwarded-Proto header from the load balancer
  # This way it will know the original request was already HTTPS
  tls:
    alpn_protocols: h2,http/1.1
  requestPolicy:
    insecure:
      action: Route  # Changed from Redirect to Route for requests behind TLS terminator
  # Enable Emissary to trust the X-Forwarded-Proto header
  xff:
    trusted_hops: 1
    num_trusted_hosts: 1
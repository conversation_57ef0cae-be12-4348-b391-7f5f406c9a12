#IAC/modules/eks-addons/argocd/values.yaml
# Global settings
global:
  domain: ${argocd_hostname}
  nodeSelector: 
%{ for i, key in node_selector_keys ~}
    ${key}: ${node_selector_values[i]}
%{ endfor ~}

# Argo CD configuration
configs:
  # General Argo CD configuration
  cm:
    timeout.reconciliation: 180s
    timeout.hard.reconciliation: 0s

  # Secret configurations
  secret:
    argocdServerAdminPassword: ${admin_password_hash}

  # Parameters
  params:
    #server.basehref: /argocd  
    server.insecure: ${server_insecure}
    #server.rootpath: /argocd
  
  # RBAC policy configuration
  rbac:
    policy.default: ${rbac_policy_default}
    policy.csv: |-
      ${indent(6, rbac_policy_csv)}

# Server configuration
server:
  extraArgs:
    - --insecure=${server_insecure}
  resources:
    limits:
      cpu: ${server_limits_cpu}
      memory: ${server_limits_memory}
    requests:
      cpu: ${server_requests_cpu}
      memory: ${server_requests_memory}
  nodeSelector: 
%{ for i, key in node_selector_keys ~}
    ${key}: ${node_selector_values[i]}
%{ endfor ~}
  
  # Service configuration
  service:
    type: ClusterIP
  
  # ConfigMaps
  configEnabled: true
  
  # Metrics
  metrics:
    enabled: true
    serviceMonitor:
      enabled: false

# Repository server configuration
repoServer:
  resources:
    limits:
      cpu: ${repo_limits_cpu}
      memory: ${repo_limits_memory}
    requests:
      cpu: ${repo_requests_cpu}
      memory: ${repo_requests_memory}
  nodeSelector: 
%{ for i, key in node_selector_keys ~}
    ${key}: ${node_selector_values[i]}
%{ endfor ~}
  
  # Metrics
  metrics:
    enabled: true
    serviceMonitor:
      enabled: false

# Application controller
controller:
  resources:
    limits:
      cpu: ${controller_limits_cpu}
      memory: ${controller_limits_memory}
    requests:
      cpu: ${controller_requests_cpu}
      memory: ${controller_requests_memory}
  nodeSelector: 
%{ for i, key in node_selector_keys ~}
    ${key}: ${node_selector_values[i]}
%{ endfor ~}
  
  # Metrics
  metrics:
    enabled: true
    serviceMonitor:
      enabled: false

# Redis configuration
redis:
  enabled: true
  nodeSelector: 
%{ for i, key in node_selector_keys ~}
    ${key}: ${node_selector_values[i]}
%{ endfor ~}

# Redis HA for high availability setup
redis-ha:
  enabled: ${ha_enabled}

# Dex configuration for SSO
dex:
  enabled: ${enable_dex}
  nodeSelector: 
%{ for i, key in node_selector_keys ~}
    ${key}: ${node_selector_values[i]}
%{ endfor ~}

# HA mode configuration
ha:
  enabled: ${ha_enabled}

# ApplicationSet controller
applicationSet:
  enabled: true
  nodeSelector: 
%{ for i, key in node_selector_keys ~}
    ${key}: ${node_selector_values[i]}
%{ endfor ~}

# Notifications controller
notifications:
  enabled: true
  nodeSelector: 
%{ for i, key in node_selector_keys ~}
    ${key}: ${node_selector_values[i]}
%{ endfor ~}
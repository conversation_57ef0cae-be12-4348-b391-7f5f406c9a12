
# IAC/modules/eks-addons/argocd/variables.tf

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace for ArgoCD"
  type        = string
  default     = "argocd"
}

variable "chart_version" {
  description = "Version of the ArgoCD Helm chart"
  type        = string
  default     = "7.8.0"
}

variable "node_selector" {
  description = "Node selector for the ArgoCD pods"
  type        = map(string)
  default     = {}
}

variable "server_resources" {
  description = "Resource limits and requests for ArgoCD server"
  type        = map(map(string))
  default     = {
    requests = {
      cpu    = "300m"
      memory = "512Mi"
    }
    limits = {
      cpu    = "600m"
      memory = "1024Mi"
    }
  }
}

variable "repo_server_resources" {
  description = "Resource limits and requests for ArgoCD repo server"
  type        = map(map(string))
  default     = {
    requests = {
      cpu    = "200m"
      memory = "256Mi"
    }
    limits = {
      cpu    = "400m"
      memory = "512Mi"
    }
  }
}

variable "controller_resources" {
  description = "Resource limits and requests for ArgoCD controller"
  type        = map(map(string))
  default     = {
    requests = {
      cpu    = "200m"
      memory = "256Mi"
    }
    limits = {
      cpu    = "400m"
      memory = "512Mi"
    }
  }
}

variable "tags" {
  description = "Tags to apply to AWS resources"
  type        = map(string)
  default     = {}
}

# Emissary integration variables
variable "emissary_enabled" {
  description = "Enable Emissary integration for ArgoCD"
  type        = bool
  default     = true
}

variable "emissary_namespace" {
  description = "Namespace where Emissary is deployed"
  type        = string
  default     = "emissary"
}

variable "argocd_hostname" {
  description = "Hostname for ArgoCD (leave empty to use any hostname)"
  type        = string
  default     = ""
}

variable "argocd_path" {
  description = "Path prefix for ArgoCD in the URL"
  type        = string
  default     = "/argocd"
}

variable "enable_dex" {
  description = "Enable Dex for SSO authentication"
  type        = bool
  default     = true
}

variable "ha_enabled" {
  description = "Enable High Availability mode for ArgoCD"
  type        = bool
  default     = false
}

variable "server_insecure" {
  description = "Run server without TLS"
  type        = bool
  default     = false
}

variable "rbac_policy_default" {
  description = "Default RBAC policy"
  type        = string
  default     = "role:readonly"
}

variable "rbac_policy_csv" {
  description = "RBAC policy CSV content"
  type        = string
  default     = ""
}


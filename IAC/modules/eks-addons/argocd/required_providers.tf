# IAC/modules/eks-addons/argocd/required_providers.tf

terraform {
  required_providers {
    helm = {
      source  = "hashicorp/helm"
      version = ">= 2.0.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.0.3"
    }
    random = {
      source  = "hashicorp/random"
      version = ">= 3.1.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.0.0"
    }
    kubectl = {
      source  = "gavin<PERSON><PERSON>/kubectl"
      version = ">= 1.14.0"
    }
  }
}
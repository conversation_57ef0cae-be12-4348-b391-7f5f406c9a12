# IAC/modules/eks-addons/argocd/main.tf
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

provider "kubectl" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
  load_config_file       = false
}

resource "kubernetes_namespace" "argocd" {
  metadata {
    name = var.namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/part-of"    = "argocd"
    }
  }
}

# Generate a random admin password with specific character requirements
resource "random_password" "admin_password" {
  length           = 10
  special          = true
  override_special = "!*$"
  min_lower        = 2
  min_upper        = 2
  min_numeric      = 2
  min_special      = 2
}

# Store the admin password in AWS Secrets Manager
resource "aws_secretsmanager_secret" "admin_password" {
  name        = "${var.cluster_name}-argocd-admin-password"
  description = "ArgoCD admin password"
  tags        = var.tags
}

resource "aws_secretsmanager_secret_version" "admin_password" {
  secret_id     = aws_secretsmanager_secret.admin_password.id
  secret_string = random_password.admin_password.result
}

locals {
  # Convert the random password to bcrypt hash format as expected by ArgoCD
  admin_password_hash = bcrypt(random_password.admin_password.result)
}

resource "helm_release" "argocd" {
  name             = "argocd"
  repository       = "https://argoproj.github.io/argo-helm"
  chart            = "argo-cd"
  version          = var.chart_version
  namespace        = kubernetes_namespace.argocd.metadata[0].name
  create_namespace = false
  timeout          = 600
  wait             = true
  
  values = [
    templatefile("${path.module}/values.yaml", {
      argocd_hostname     = var.argocd_hostname
      argocd_path         = var.argocd_path
      admin_password_hash = local.admin_password_hash
      node_selector_keys   = keys(var.node_selector)
      node_selector_values = values(var.node_selector)
      
      server_requests_cpu = var.server_resources.requests.cpu
      server_requests_memory = var.server_resources.requests.memory
      server_limits_cpu = var.server_resources.limits.cpu
      server_limits_memory = var.server_resources.limits.memory
      
      repo_requests_cpu = var.repo_server_resources.requests.cpu
      repo_requests_memory = var.repo_server_resources.requests.memory
      repo_limits_cpu = var.repo_server_resources.limits.cpu
      repo_limits_memory = var.repo_server_resources.limits.memory
      
      controller_requests_cpu = var.controller_resources.requests.cpu
      controller_requests_memory = var.controller_resources.requests.memory
      controller_limits_cpu = var.controller_resources.limits.cpu
      controller_limits_memory = var.controller_resources.limits.memory
      
      server_insecure     = var.server_insecure
      enable_dex          = var.enable_dex
      ha_enabled          = var.ha_enabled
      rbac_policy_default = var.rbac_policy_default
      rbac_policy_csv     = var.rbac_policy_csv
    })
  ]

  depends_on = [
    kubernetes_namespace.argocd,
    aws_secretsmanager_secret_version.admin_password,
    kubernetes_secret.argocd_bitbucket_ssh_key
  ]
}

# Create Emissary Mapping
resource "kubectl_manifest" "argocd_mapping" {
  count = var.emissary_enabled ? 1 : 0
  
  yaml_body = templatefile("${path.module}/emissary-mapping.yaml", {
    emissary_namespace = var.emissary_namespace
    hostname_value     = var.argocd_hostname != "" ? var.argocd_hostname : "*"
    argocd_path        = var.argocd_path
    argocd_namespace   = kubernetes_namespace.argocd.metadata[0].name
  })

  depends_on = [
    helm_release.argocd
  ]
}

# Create Emissary Host if hostname is specified
resource "kubectl_manifest" "argocd_host" {
  count = var.emissary_enabled && var.argocd_hostname != "" ? 1 : 0
  
  yaml_body = templatefile("${path.module}/emissary-host.yaml", {
    emissary_namespace = var.emissary_namespace
    argocd_hostname    = var.argocd_hostname
  })

  depends_on = [
    helm_release.argocd
  ]
}
# IAC/modules/eks-addons/argocd/ssh-key.tf

# Generate an RSA key pair for ArgoCD Bitbucket integration
resource "tls_private_key" "argocd_bitbucket_ssh" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

# Store the private key in Secrets Manager
resource "aws_secretsmanager_secret" "argocd_bitbucket_private_key" {
  name        = "${var.cluster_name}-argocd-bitbucket-ssh-private-key"
  description = "ArgoCD SSH private key for Bitbucket authentication"
  tags        = var.tags
}

resource "aws_secretsmanager_secret_version" "argocd_bitbucket_private_key" {
  secret_id     = aws_secretsmanager_secret.argocd_bitbucket_private_key.id
  secret_string = tls_private_key.argocd_bitbucket_ssh.private_key_pem
}

# Store the public key in Secrets Manager
resource "aws_secretsmanager_secret" "argocd_bitbucket_public_key" {
  name        = "${var.cluster_name}-argocd-bitbucket-ssh-public-key"
  description = "ArgoCD SSH public key for Bitbucket authentication"
  tags        = var.tags
}

resource "aws_secretsmanager_secret_version" "argocd_bitbucket_public_key" {
  secret_id     = aws_secretsmanager_secret.argocd_bitbucket_public_key.id
  secret_string = tls_private_key.argocd_bitbucket_ssh.public_key_openssh
}

# Create a Kubernetes secret to hold the private key for ArgoCD
resource "kubernetes_secret" "argocd_bitbucket_ssh_key" {
  metadata {
    name      = "argocd-bitbucket-ssh-key"
    namespace = kubernetes_namespace.argocd.metadata[0].name
    labels = {
      "app.kubernetes.io/part-of" = "argocd"
    }
  }

  data = {
    "sshPrivateKey" = tls_private_key.argocd_bitbucket_ssh.private_key_pem
  }

  depends_on = [
    kubernetes_namespace.argocd
  ]
}

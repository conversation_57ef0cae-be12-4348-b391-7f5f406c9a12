#IAC/modules/eks-addons/karpenter_new/values.yaml
installCRDs: true
controller:
  resources:
    requests:
      cpu: ${controller_resources_requests_cpu}
      memory: ${controller_resources_requests_memory}
    limits:
      cpu: ${controller_resources_limits_cpu}
      memory: ${controller_resources_limits_memory}
  waitForCRDs: true

logLevel: ${log_level}

serviceMonitor:
  enabled: ${service_monitor_enabled}

%{ if use_pod_identity }
serviceAccount:
  annotations:
    eks.amazonaws.com/audience: sts.amazonaws.com
    eks.amazonaws.com/token-expiration: "86400"
%{ endif }
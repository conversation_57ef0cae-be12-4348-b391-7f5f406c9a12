#IAC/modules/eks-addons/karpenter_new/variables.tf
variable "cluster_name" {
  type        = string
  description = "Name of the EKS cluster"
}

variable "cluster_endpoint" {
  type        = string
  description = "Endpoint of the EKS cluster"
}

variable "node_iam_role_name" {
  description = "eks managed node role name"
  type        = string
}



variable "oidc_provider" {
  type        = string
  description = "OIDC provider URL associated with the EKS cluster"
}

variable "eks_oidc_provider_arn" {
  description = "The ARN of the EKS OIDC Provider"
  type        = string
}

variable "karpenter_version" {
  description = "Version of Karpenter to install"
  type        = string
  default     = "1.1.2"
}

variable "node_iam_role_arn" {
  description = "eks managed node role arn"
  type        = string
}



variable "node_security_group_id" {
  description = "eks managed node group security id"
  type        = string
}

variable "create_spot_service_linked_role" {
  description = "Whether to create the service linked role for spot instances"
  type        = bool
  default     = true
}

# Controller resource configurations
variable "controller_resources_requests_cpu" {
  description = "CPU requests for the Karpenter controller"
  type        = string
  default     = "200m"
}

variable "controller_resources_requests_memory" {
  description = "Memory requests for the Karpenter controller"
  type        = string
  default     = "200Mi"
}

variable "controller_resources_limits_cpu" {
  description = "CPU limits for the Karpenter controller"
  type        = string
  default     = "500m"
}

variable "controller_resources_limits_memory" {
  description = "Memory limits for the Karpenter controller"
  type        = string
  default     = "400Mi"
}

# NodePool configurations
variable "nodepool_name" {
  description = "Name of the NodePool"
  type        = string
  default     = "karpenter-nodepool"
}

variable "nodepool_instance_types" {
  description = "List of instance types for the NodePool"
  type        = list(string)
  default     = ["t3.medium", "t3.large", "t3a.medium", "t3a.large"]
}

variable "nodepool_zones" {
  description = "List of availability zones for the NodePool"
  type        = list(string)
  default     = ["eu-west-2a", "eu-west-2b", "eu-west-2c"]
}

variable "nodepool_limits_cpu" {
  description = "CPU limits for the NodePool"
  type        = string
  default     = "6"
}

variable "nodepool_limits_memory" {
  description = "Memory limits for the NodePool"
  type        = string
  default     = "24Gi"
}

variable "nodepool_spot_to_ondemand_ratio" {
  description = "Preference ratio for spot instances over on-demand (higher value = stronger spot preference)"
  type        = number
  default     = 10
}

# NodeClass configurations
variable "nodeclass_name" {
  description = "Name of the EC2NodeClass"
  type        = string
  default     = "karpenter-nodeclass"
}

variable "nodeclass_ami_family" {
  description = "AMI family for the EC2NodeClass"
  type        = string
  default     = "al2023"
}

variable "nodeclass_subnet_tags" {
  description = "Tags to select subnets for the EC2NodeClass"
  type        = map(string)
  default     = {}
}

variable "nodeclass_security_group_tags" {
  description = "Tags to select security groups for the EC2NodeClass"
  type        = map(string)
  default     = {}
}

variable "disruption_consolidation_policy" {
  description = "Consolidation policy for the NodePool"
  type        = string
  default     = "WhenEmptyOrUnderutilized"
}

variable "disruption_consolidation_time" {
  description = "Consolidation policy for the NodePool"
  type        = string
  default     = "30s"
}

variable "use_pod_identity" {
  description = "Whether to use Pod Identity instead of IRSA"
  type        = bool
  default     = true
}

variable "log_level" {
  description = "Log level for Karpenter"
  type        = string
  default     = "info"
}

variable "service_monitor_enabled" {
  description = "Whether to enable ServiceMonitor for Prometheus"
  type        = bool
  default     = false
}

variable "nodeclass_version" {
  description = "Version identifier for the EC2NodeClass that should be incremented when node_iam_role_name changes"
  type        = string
  default     = "v1"
}
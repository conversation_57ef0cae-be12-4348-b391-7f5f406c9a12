#IAC/modules/eks-addons/karpenter_new/node-class.yaml
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: ${nodeclass_name}
spec:
  amiSelectorTerms:
    - alias: "${ami_family}@latest"
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: ${cluster_name}
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: ${cluster_name}
  role: ${node_role_name}
  tags:
    karpenter.sh/discovery: ${cluster_name}
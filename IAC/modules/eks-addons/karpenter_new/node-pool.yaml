#IAC/modules/eks-addons/karpenter_new/node-pool.yaml
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: "${nodepool_name}"
spec:
  template:
    spec:
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: ${nodeclass_name}
      requirements:
        - key: "karpenter.sh/capacity-type"
          operator: In
          values: ["spot"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["amd64"]
        - key: "karpenter.k8s.aws/instance-category"
          operator: In
          values: ["t", "m"]
        - key: "karpenter.k8s.aws/instance-cpu"
          operator: In
          values: ["2", "4"]
  limits:
    cpu: ${nodepool_limits_cpu}
    memory: ${nodepool_limits_memory}
  disruption:
    consolidationPolicy: "${disruption_consolidation_policy}"
    consolidateAfter: "${disruption_consolidation_time}"
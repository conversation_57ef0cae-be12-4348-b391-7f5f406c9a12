#IAC/modules/eks-addons/karpenter_new/main.tf
provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

module "karpenter" {
  source  = "terraform-aws-modules/eks/aws//modules/karpenter"
  version = "20.33.0"

  cluster_name = var.cluster_name
  enable_v1_permissions = true
  create_node_iam_role = false
  create_access_entry = false
  node_iam_role_use_name_prefix   = false
  node_iam_role_name = var.node_iam_role_name
  node_iam_role_arn    = var.node_iam_role_arn
  create_pod_identity_association = true
  

  iam_role_tags = {
    "karpenter.sh/discovery" = var.cluster_name
    "ManagedBy"              = "karpenter"
    "ClusterName"            = var.cluster_name
  }

  node_iam_role_additional_policies = {
    AmazonSSMManagedInstanceCore = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  }
}

resource "aws_iam_service_linked_role" "spot" {
  count = var.create_spot_service_linked_role ? 1 : 0
  aws_service_name = "spot.amazonaws.com"
}

data "aws_caller_identity" "current" {}

resource "helm_release" "karpenter" {
  namespace        = "kube-system"
  name       = "karpenter"
  repository = "oci://public.ecr.aws/karpenter"
  chart      = "karpenter"
  version    = var.karpenter_version
  timeout = 300
  values = [
    <<-EOT
    nodeSelector:
      karpenter.sh/controller: 'true'
    dnsPolicy: Default
    settings:
      clusterName: ${var.cluster_name}
      clusterEndpoint: ${var.cluster_endpoint}
      interruptionQueueName: ${module.karpenter.queue_name}
    EOT
  ]
}

# Create NodeClass manifest using template
resource "kubectl_manifest" "karpenter_node_class" {
  yaml_body = templatefile("${path.module}/node-class.yaml",  {
    nodeclass_name = var.nodeclass_name
    ami_family     = var.nodeclass_ami_family
    cluster_name   = var.cluster_name
    node_role_name = var.node_iam_role_name
  })

  depends_on = [
    helm_release.karpenter
  ]
}

resource "kubectl_manifest" "karpenter_node_pool" {
  yaml_body = templatefile("${path.module}/node-pool.yaml", {
    nodepool_name = var.nodepool_name
    nodeclass_name = var.nodeclass_name
    nodepool_limits_cpu = var.nodepool_limits_cpu
    nodepool_limits_memory = var.nodepool_limits_memory
    disruption_consolidation_policy = var.disruption_consolidation_policy
    disruption_consolidation_time = var.disruption_consolidation_time
  })
  depends_on = [
    helm_release.karpenter,
    kubectl_manifest.karpenter_node_class
  ]
}
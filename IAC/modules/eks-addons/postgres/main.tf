#IAC/modules/eks-addons/postgres/main.tf
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

# Generate a random password
resource "random_password" "postgres_password" {
  length           = 10
  special          = true
  override_special = "!*"
  min_lower        = 2
  min_upper        = 2
  min_numeric      = 2
  min_special      = 2
}

# Store the password in AWS Secrets Manager
resource "aws_secretsmanager_secret" "postgres_password" {
  name        = "${var.cluster_name}-postgres-sonarqube-password"
  description = "PostgreSQL password for SonarQube database"
  tags        = var.tags
}

resource "aws_secretsmanager_secret_version" "postgres_password" {
  secret_id     = aws_secretsmanager_secret.postgres_password.id
  secret_string = random_password.postgres_password.result
}

resource "kubernetes_namespace" "postgres" {
  metadata {
    name = var.namespace
  }
}

resource "kubernetes_secret" "postgres_password" {
  metadata {
    name      = "postgres-password"
    namespace = kubernetes_namespace.postgres.metadata[0].name
  }

  data = {
    "postgres-password" = random_password.postgres_password.result
  }

  type = "Opaque"
}


resource "helm_release" "postgres" {
  name       = "postgres"
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "postgresql"
  version    = var.chart_version
  namespace  = kubernetes_namespace.postgres.metadata[0].name

  values = [
    templatefile("${path.module}/values.yaml", {
      storage_class = "gp2"
      persistence_size = var.storage_size
      postgres_username = var.postgres_username
      postgres_database = var.postgres_database
      node_selector = var.node_selector
    })
  ]

  set {
    name  = "global.postgresql.auth.postgresPassword"
    value = random_password.postgres_password.result
  }

  depends_on = [
    kubernetes_namespace.postgres,
    kubernetes_secret.postgres_password
  ]
}
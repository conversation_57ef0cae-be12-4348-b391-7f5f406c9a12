#IAC/modules/eks-addons/postgres/variables.tf
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace to deploy PostgreSQL"
  type        = string
  default     = "postgres"
}

variable "chart_version" {
  description = "Version of the PostgreSQL Helm chart"
  type        = string
  default     = "16.1.0"
}

variable "storage_size" {
  description = "Size of the persistent volume claims"
  type        = string
  default     = "20Gi"
}

variable "postgres_username" {
  description = "PostgreSQL username"
  type        = string
  default     = "postgres"
}

variable "postgres_database" {
  description = "PostgreSQL database name"
  type        = string
  default     = "sonardb"
}

variable "node_selector" {
  description = "Node selector for PostgreSQL pods"
  type        = map(string)
  default     = {}
}

variable "tags" {
  description = "Tags to apply to AWS resources"
  type        = map(string)
  default     = {}
}
#IAC/modules/eks-addons/postgres/values.yaml
global:
  postgresql:
    auth:
      username: ${postgres_username}
      database: ${postgres_database}

primary:
  nodeSelector: ${jsonencode(node_selector)}
  persistence:
    enabled: true
    storageClass: ${storage_class}
    size: ${persistence_size}
  resources:
    limits:
      memory: 1Gi
      cpu: 1000m
    requests:
      memory: 256Mi
      cpu: 250m

# Set proper security context for PostgreSQL to work correctly
volumePermissions:
  enabled: true

metrics:
  enabled: false  # Set to true if you want to enable Prometheus metrics

# Configure the service
service:
  type: ClusterIP
  port: 5432
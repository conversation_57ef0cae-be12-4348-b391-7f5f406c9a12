#IAC/modules/eks-addons/postgres/outputs.tf
output "postgres_namespace" {
  description = "Namespace where PostgreSQL is deployed"
  value       = kubernetes_namespace.postgres.metadata[0].name
}

output "postgres_service_name" {
  description = "Name of the PostgreSQL service"
  value       = "${helm_release.postgres.name}-postgresql"
}

output "postgres_connection_string" {
  description = "Connection string for PostgreSQL"
  value       = "jdbc:postgresql://${helm_release.postgres.name}-postgresql.${kubernetes_namespace.postgres.metadata[0].name}.svc.cluster.local:5432/${var.postgres_database}"
  sensitive   = false
}

output "postgres_username" {
  description = "PostgreSQL username"
  value       = var.postgres_username
  sensitive   = false
}

output "postgres_password" {
  description = "PostgreSQL password"
  value       = random_password.postgres_password.result
  sensitive   = true
}

output "postgres_password_secret_name" {
  description = "Name of the Kubernetes secret containing PostgreSQL password"
  value       = kubernetes_secret.postgres_password.metadata[0].name
}

output "postgres_password_secret_arn" {
  description = "ARN of the AWS Secrets Manager secret containing PostgreSQL password"
  value       = aws_secretsmanager_secret.postgres_password.arn
}
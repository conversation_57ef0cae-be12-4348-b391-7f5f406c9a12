#IAC/modules/eks-addons/argocd-slack-integration/outputs.tf
output "external_secret_name" {
  description = "Name of the ExternalSecret resource created"
  value       = "argocd-slack-token"
}

output "kubernetes_secret_name" {
  description = "Name of the Kubernetes secret created by ExternalSecret"
  value       = "argocd-notifications-secret"
}

output "slack_integration_status" {
  description = "Status of Slack integration"
  value       = "Configured - requires Slack channel subscription to application resources"
}
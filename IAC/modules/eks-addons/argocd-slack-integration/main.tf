#IAC/modules/eks-addons/argocd-slack-integration/main.tf
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

provider "kubectl" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
  load_config_file       = false
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

# Create the ExternalSecret resource to fetch the Slack token
resource "kubectl_manifest" "argocd_slack_external_secret" {
  yaml_body = templatefile("${path.module}/slack-external-secret.yaml", {
    secret_name        = var.slack_token_secret_name
    namespace          = var.argocd_namespace
    secret_key         = var.slack_token_secret_key
  })
  
  wait               = true
  server_side_apply  = true
  force_conflicts    = true
}

# Wait for ExternalSecret to sync before proceeding
resource "null_resource" "wait_for_external_secret" {
  depends_on = [kubectl_manifest.argocd_slack_external_secret]
  
  provisioner "local-exec" {
    command = <<-EOT
      count=0
      max_attempts=6
      while [ $count -lt $max_attempts ]; do
        status=$(kubectl get externalsecret argocd-slack-token -n ${var.argocd_namespace} -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' 2>/dev/null || echo "NotFound")
        if [ "$status" == "True" ]; then
          echo "ExternalSecret successfully synced"
          exit 0
        fi
        echo "Waiting for ExternalSecret to sync... (attempt $count/$max_attempts)"
        count=$((count+1))
        sleep 10
      done
      echo "Warning: Could not confirm ExternalSecret sync status, continuing anyway"
    EOT
    
    interpreter = ["/bin/bash", "-c"]
  }
}

# Update the ConfigMap with all required content in a single resource
resource "kubectl_manifest" "argocd_notifications_cm_complete" {
  depends_on = [null_resource.wait_for_external_secret]
  
  yaml_body = templatefile("${path.module}/notifications-cm-complete.yaml", {
    namespace = var.argocd_namespace
    argocd_url = var.argocd_url
    slack_username = var.slack_username
    slack_icon = var.slack_icon
    slack_channel = var.slack_channel
  })
  
  server_side_apply = true
  force_conflicts   = true
}

# Validate that the ConfigMap has the Slack service configuration
resource "null_resource" "validate_configmap" {
  depends_on = [kubectl_manifest.argocd_notifications_cm_complete]
  
  provisioner "local-exec" {
    command = <<-EOT
      echo "Validating ConfigMap configuration..."
      kubectl get configmap argocd-notifications-cm -n ${var.argocd_namespace} -o yaml | grep "service.slack" || echo "WARNING: service.slack configuration might be missing"
    EOT
    
    interpreter = ["/bin/bash", "-c"]
  }
}

# Restart the notification controller to apply changes
resource "null_resource" "restart_notification_controller" {
  depends_on = [
    kubectl_manifest.argocd_notifications_cm_complete,
    null_resource.validate_configmap
  ]

  triggers = {
    templates_updated = kubectl_manifest.argocd_notifications_cm_complete.yaml_body
  }

  # Use a safer approach to restart the controller with namespace existence check
  provisioner "local-exec" {
    command = <<-EOT
      echo "Checking if namespace exists..."
      if kubectl get namespace ${var.argocd_namespace} &>/dev/null; then
        echo "Restarting ArgoCD notification controller..."
        kubectl rollout restart deployment argocd-notifications-controller -n ${var.argocd_namespace}
        echo "Waiting for restart to complete..."
        kubectl rollout status deployment argocd-notifications-controller -n ${var.argocd_namespace} --timeout=60s
      else
        echo "Namespace ${var.argocd_namespace} not found, skipping controller restart"
      fi
    EOT
    
    interpreter = ["/bin/bash", "-c"]
  }
}
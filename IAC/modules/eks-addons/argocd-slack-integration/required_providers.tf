#IAC/modules/eks-addons/argocd-slack-integration/required_providers.tf
terraform {
  required_providers {
    kubectl = {
      source  = "gavi<PERSON><PERSON><PERSON>/kubectl"
      version = ">= 1.14.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.0.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.0.3"
    }
    null = {
      source  = "hashicorp/null"
      version = ">= 3.1.0"
    }
  }
}
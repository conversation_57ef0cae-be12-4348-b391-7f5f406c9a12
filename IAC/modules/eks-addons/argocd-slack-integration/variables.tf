#IAC/modules/eks-addons/argocd-slack-integration/variables.tf
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "argocd_namespace" {
  description = "Namespace where ArgoCD is installed"
  type        = string
  default     = "argocd"
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "eu-west-2"
}

variable "slack_token_secret_name" {
  description = "Name of the secret in AWS Secrets Manager containing the Slack token"
  type        = string
}

variable "slack_token_secret_key" {
  description = "The key within the AWS Secrets Manager secret that contains the Slack token (leave empty if token is stored as plain string)"
  type        = string
  default     = ""
}

variable "argocd_url" {
  description = "The URL for ArgoCD UI"
  type        = string
  default     = ""
}

variable "slack_channel" {
  description = "Default Slack channel for notifications"
  type        = string
  default     = "#deployments"
}

variable "slack_username" {
  description = "Username to display for Slack notifications"
  type        = string
  default     = "ArgoCD"
}

variable "slack_icon" {
  description = "Icon URL for Slack notifications"
  type        = string
  default     = "https://argocd-notifications.readthedocs.io/en/stable/assets/logo.png"
}
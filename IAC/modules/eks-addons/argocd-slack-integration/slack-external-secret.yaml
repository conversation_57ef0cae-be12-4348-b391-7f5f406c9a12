#IAC/modules/eks-addons/argocd-slack-integration/slack-external-secret.yaml
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: argocd-slack-token
  namespace: ${namespace}
spec:
  refreshInterval: "1h"
  secretStoreRef:
    name: aws-secrets
    kind: ClusterSecretStore
  target:
    name: argocd-notifications-secret
    creationPolicy: Owner
  data:
  - secretKey: slack-token
    remoteRef:
      key: ${secret_name}
      %{ if secret_key != "" }
      property: ${secret_key}
      %{ endif }
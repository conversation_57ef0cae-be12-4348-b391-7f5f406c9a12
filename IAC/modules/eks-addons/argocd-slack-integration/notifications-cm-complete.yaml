#IAC/modules/eks-addons/argocd-slack-integration/notifications-cm-complete.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
  namespace: ${namespace}
data:
  # Context configuration
  context: |
    argocdUrl: ${argocd_url}
  
  # Slack service configuration
  service.slack: |
    token: $slack-token
    username: ${slack_username}
    icon: ${slack_icon}
  
  # Application deployment status templates with simplified formatting
  template.app-deployed: |
    message: |
      :rocket: *Application Deployed Successfully*
      
      *Application:* {{.app.metadata.name}}
      *Namespace:* {{if .app.spec.destination.namespace}}{{.app.spec.destination.namespace}}{{else}}default{{end}}
      *Project:* {{.app.spec.project}}
      *Revision:* `{{.app.status.sync.revision | trunc 7}}`
      
      {{if .app.status.summary.images}}*Images:*
      ```
      {{range .app.status.summary.images}}
      • {{.}}
      {{end}}
      ```{{end}}
      
      :link: <{{.context.argocdUrl}}/applications/{{.app.metadata.name}}|View in ArgoCD>
  
  template.app-sync-failed: |
    message: |
      :x: *Sync Operation Failed*
      
      *Application:* {{.app.metadata.name}}
      *Namespace:* {{if .app.spec.destination.namespace}}{{.app.spec.destination.namespace}}{{else}}default{{end}}
      
      {{if .app.status.operationState.message}}*Error:* {{.app.status.operationState.message}}{{end}}
      
      {{if .app.status.operationState.syncResult.resources}}*Failed Resources:*
      ```
      {{range .app.status.operationState.syncResult.resources}}{{if ne .status "Synced"}}
      • {{.kind}}/{{.name}}: {{.message}}
      {{end}}{{end}}
      ```{{end}}
      
      :link: <{{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true|View Failed Operation>
  
  template.app-sync-status-unknown: |
    message: |
      :question: *Application Sync Status Unknown*
      
      *Application:* {{.app.metadata.name}}
      *Namespace:* {{if .app.spec.destination.namespace}}{{.app.spec.destination.namespace}}{{else}}default{{end}}
      *Last Known Revision:* {{.app.status.sync.revision}}
      
      This could indicate connectivity issues with the cluster or resource status issues.
      
      :link: <{{.context.argocdUrl}}/applications/{{.app.metadata.name}}|View in ArgoCD>
  
  template.app-health-degraded: |
    message: |
      :rotating_light: *Application Health Degraded*
      
      *Application:* {{.app.metadata.name}}
      *Namespace:* {{if .app.spec.destination.namespace}}{{.app.spec.destination.namespace}}{{else}}default{{end}}
      *Sync Status:* {{.app.status.sync.status}}
      
      {{if .app.status.health.message}}*Issue:* {{.app.status.health.message}}{{end}}
      
      {{if .app.status.resources}}*Unhealthy Resources:*
      ```
      {{range .app.status.resources}}{{if eq .health.status "Degraded"}}
      • {{.kind}}/{{.name}}: {{.health.message}}
      {{end}}{{end}}
      ```{{end}}
      
      :link: <{{.context.argocdUrl}}/applications/{{.app.metadata.name}}|View in ArgoCD>
  
  template.app-sync-running: |
    message: |
      :arrows_counterclockwise: *Sync in Progress*
      
      *Application:* {{.app.metadata.name}}
      *Started at:* {{.app.status.operationState.startedAt}}
      *Initiated by:* {{if .app.status.operationState.initiatedBy.automated}}Automated{{else}}{{.app.status.operationState.initiatedBy.username}}{{end}}
      
      {{if .app.status.operationState.message}}*Message:* {{.app.status.operationState.message}}{{end}}
      
      {{if .app.status.operationState.operation.sync.resources}}*Resources being synced:*
      ```
      {{range .app.status.operationState.operation.sync.resources}}
      • {{.kind}}/{{.name}}
      {{end}}
      ```{{end}}
      
      :link: <{{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true|View Operation Details>
  
  template.app-sync-succeeded: |
    message: |
      :white_check_mark: *Sync Completed Successfully*
      
      *Application:* {{.app.metadata.name}}
      *Revision:* `{{.app.status.sync.revision | trunc 7}}`
      {{if .app.status.operationState.operation.sync.revision}}*Synced from:* `{{.app.status.operationState.operation.sync.revision | trunc 7}}`{{end}}
      
      {{if .app.status.operationState.message}}*Details:* {{.app.status.operationState.message}}{{end}}
      *Started:* {{.app.status.operationState.startedAt}}
      *Finished:* {{.app.status.operationState.finishedAt}}
      
      :link: <{{.context.argocdUrl}}/applications/{{.app.metadata.name}}|View in ArgoCD>
  
  # Application triggers
  trigger.on-deployed: |
    - when: app.status.sync.status == 'Synced' and app.status.health.status == 'Healthy'
      send: [app-deployed]
  
  trigger.on-sync-failed: |
    - when: app.status.operationState.phase in ['Error', 'Failed']
      send: [app-sync-failed]
  
  trigger.on-sync-status-unknown: |
    - when: app.status.sync.status == 'Unknown'
      send: [app-sync-status-unknown]
  
  trigger.on-health-degraded: |
    - when: app.status.health.status == 'Degraded'
      send: [app-health-degraded]
  
  trigger.on-sync-running: |
    - when: app.status.operationState.phase == 'Running'
      send: [app-sync-running]
  
  trigger.on-sync-succeeded: |
    - when: app.status.operationState.phase == 'Succeeded'
      send: [app-sync-succeeded]

  # Subscription configuration
  subscriptions: |
    # Default subscription for all applications
    - recipients:
      - slack:${slack_channel}
      triggers:
      - on-deployed
      - on-sync-succeeded
      - on-sync-failed
      - on-health-degraded
      - on-sync-running
      - on-sync-status-unknown
    
    # Optional: specific subscription for critical applications
    # - recipients:
    #   - slack:#critical-alerts
    #   selector:
    #     apps:
    #       - production-*
    #   triggers:
    #   - on-health-degraded
    #   - on-sync-failed
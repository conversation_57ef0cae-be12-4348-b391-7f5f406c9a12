#IAC/modules/eks-addons/external-secrets-operator/main.tf
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

locals {
  es-service-account-name = "external-secrets"
  namespace               = "external-secrets"
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority.0.data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

module "external_secrets_irsa_role" {
  source = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"

  role_name                      = "${var.cluster_name}-external-secrets"
  attach_external_secrets_policy = true  # This will attach the module's pre-defined broad policy

  oidc_providers = {
    ex = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["${local.namespace}:${local.es-service-account-name}"]
    }
  }
}

resource "helm_release" "external-secrets" {
  repository       = "https://charts.external-secrets.io"
  chart            = "external-secrets"
  name             = "external-secrets"
  namespace        = local.namespace
  create_namespace = true

  set {
    name  = "serviceAccount.create"
    value = "true"
  }

  set {
    name  = "serviceAccount.name"
    value = local.es-service-account-name
  }

  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = module.external_secrets_irsa_role.iam_role_arn
  }

  set {
    name  = "installCRDs"
    value = "true"
  }
}

resource "kubectl_manifest" "clusterSecretStore" {
  depends_on = [helm_release.external-secrets]

  yaml_body = templatefile("${path.module}/clusterSecretStore.yaml", {
    aws_region       = var.aws_region
  })
}
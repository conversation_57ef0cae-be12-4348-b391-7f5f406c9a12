#IAC/modules/eks-addons/metrics-server/values.yaml
metrics:
  enabled: true

apiService:
  create: true

args:
  - "--kubelet-insecure-tls"
replicas: "${replicas_count}"

resources:
  requests:
    cpu: "${resource_requests_cpu}"
    memory: "${resource_requests_memory}"
  limits:
    cpu: "${resource_limits_cpu}"
    memory: "${resource_limits_memory}"
hostNetwork:
  enabled: false

livenessProbe:
  initialDelaySeconds: 20

readinessProbe:
  initialDelaySeconds: 20
serviceMonitor:
  enabled: false
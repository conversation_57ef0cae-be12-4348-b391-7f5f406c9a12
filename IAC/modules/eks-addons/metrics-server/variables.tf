
#IAC/modules/eks-addons/metrics-server/variables.tf
variable "cluster_name" {
  type        = string
  description = "Name of the EKS cluster"
}

variable "chart_version" {
  description = "Metrics server chart version"
  type = string
}

variable "resource_requests_cpu" {
  description = "CPU resource requests for SonarQube"
  type        = string
  default     = "100m"
}

variable "resource_requests_memory" {
  description = "Memory resource requests for SonarQube"
  type        = string
  default     = "200Mi"
}

variable "resource_limits_cpu" {
  description = "CPU resource limits for SonarQube"
  type        = string
  default     = "200m"
}

variable "resource_limits_memory" {
  description = "Memory resource limits for SonarQube"
  type        = string
  default     = "300Mi"
}

variable "replicas_count" {
  description = "number of replicas"
  type = number
  default = 1
}

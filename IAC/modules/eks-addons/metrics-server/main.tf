#IAC/modules/eks-addons/metrics-server/main.tf

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

resource "helm_release" "metrics_server" {
  name             = "metrics-server"
  repository       = "https://kubernetes-sigs.github.io/metrics-server"
  chart            = "metrics-server"
  namespace        = "kube-system"
  create_namespace = false
  version          = var.chart_version

  values = [
  templatefile("${path.module}/values.yaml", {
    resource_requests_cpu = var.resource_requests_cpu
    resource_requests_memory = var.resource_requests_memory
    resource_limits_cpu = var.resource_limits_cpu
    resource_limits_memory = var.resource_limits_memory
    replicas_count = var.replicas_count
  })
]
}
#IAC/modules/eks-addons/metrics-server/outputs.tf
output "metrics_server_status" {
  description = "The status of the Metrics Server deployment"
  value       = helm_release.metrics_server.status
}

output "metrics_server_namespace" {
  description = "The namespace where the Metrics Server is deployed"
  value       = helm_release.metrics_server.namespace
}

output "metrics_server_version" {
  description = "The version of the Metrics Server deployed"
  value       = helm_release.metrics_server.version
}
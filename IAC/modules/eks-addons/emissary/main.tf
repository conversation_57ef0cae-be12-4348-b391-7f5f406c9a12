# IAC/modules/eks-addons/emissary/main.tf

data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

provider "kubectl" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
  load_config_file       = false
}

resource "kubernetes_namespace" "emissary" {
  metadata {
    name = var.namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/part-of"    = "emissary-ingress"
    }
  }
}

/*data "http" "emissary_crds" {
  url = "https://app.getambassador.io/yaml/emissary/${var.emissary_version}/emissary-crds.yaml"
}

data "kubectl_file_documents" "emissary_crds" {
  content = data.http.emissary_crds.response_body
}

resource "kubectl_manifest" "emissary_crds" {
  for_each  = data.kubectl_file_documents.emissary_crds.manifests
  yaml_body = each.value

  override_namespace = var.namespace

  depends_on = [
    kubernetes_namespace.emissary
  ]
}*/

resource "helm_release" "emissary_ingress" {
  depends_on = [
    kubernetes_namespace.emissary
  ]
  
  name             = "emissary-ingress"
  chart            = "emissary-ingress"
  repository       = "https://app.getambassador.io"
  namespace        = var.namespace
  create_namespace = false
  version          = var.emissary_chart_version
  
  values = [
    templatefile("${path.module}/emissary.yaml", {
      acm_certificate_arn = var.acm_certificate_arn
    })
  ]

  # Add timeout to avoid Helm operation timeout
  timeout = 900
}

# Create a default TLSContext for HTTPS if certificate ARN is provided
resource "kubectl_manifest" "default_tls_context" {
  count = var.acm_certificate_arn != "" ? 1 : 0
  
  depends_on = [
    helm_release.emissary_ingress
  ]

  yaml_body = <<YAML
apiVersion: getambassador.io/v3alpha1
kind: TLSContext
metadata:
  name: default-tls-context
  namespace: ${var.namespace}
spec:
  hosts: ["*"]
  secret: ${var.default_tls_secret_name}
  min_tls_version: v1.2
  YAML
}
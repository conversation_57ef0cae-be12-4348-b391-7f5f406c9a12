# IAC/modules/eks-addons/emissary/emissary.yaml

replicaCount: 2

service:
  type: LoadBalancer
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "external"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: "ip"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-attributes: "load_balancing.cross_zone.enabled=true"
    # Make sure X-Forwarded-Proto is properly set by the load balancer
    service.beta.kubernetes.io/aws-load-balancer-ssl-negotiation-policy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
    service.beta.kubernetes.io/aws-load-balancer-proxied-protocol: "http"
    %{ if acm_certificate_arn != "" }
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "${acm_certificate_arn}"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "443"
    %{ endif }

# Enable default listeners for HTTP/HTTPS
createDefaultListeners: true

# Resource configuration
resources:
  limits:
    cpu: 500m
    memory: 750Mi
  requests:
    cpu: 200m
    memory: 500Mi

# Pod disruption budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Affinity rules for HA deployment
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchLabels:
            app.kubernetes.io/name: emissary-ingress
        topologyKey: kubernetes.io/hostname

# Health checks
livenessProbe:
  initialDelaySeconds: 30
  periodSeconds: 15

readinessProbe:
  initialDelaySeconds: 30
  periodSeconds: 15
# IAC/modules/eks-addons/emissary/outputs.tf

output "namespace" {
  description = "Namespace where Emissary is deployed"
  value       = kubernetes_namespace.emissary.metadata[0].name
}

output "service_name" {
  description = "Name of the Emissary service"
  value       = "emissary-ingress"
}

output "helm_release_status" {
  description = "Status of the Helm release"
  value       = helm_release.emissary_ingress.status
}

output "load_balancer_hostname" {
  description = "Hostname of the load balancer created for Emissary"
  value       = "kubectl get svc -n ${var.namespace} emissary-ingress -o jsonpath='{.status.loadBalancer.ingress[0].hostname}'"
}

output "default_tls_context" {
  description = "Whether a default TLS context was created"
  value       = var.acm_certificate_arn != ""
}
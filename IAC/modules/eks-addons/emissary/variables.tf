# IAC/modules/eks-addons/emissary/variables.tf

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "namespace" {
  description = "Namespace for Emissary resources"
  type        = string
  default     = "emissary"
}

variable "emissary_version" {
  description = "Emissary Ingress version"
  type        = string
  default     = "3.9.1"
}

variable "emissary_chart_version" {
  description = "Emissary Ingress Helm chart version"
  type        = string
  default     = "8.9.1"
}

variable "acm_certificate_arn" {
  description = "ARN of ACM certificate to use with the load balancer"
  type        = string
  default     = ""
}

variable "default_tls_secret_name" {
  description = "Name of the default TLS secret to use with Emissary"
  type        = string
  default     = "emissary-tls"
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
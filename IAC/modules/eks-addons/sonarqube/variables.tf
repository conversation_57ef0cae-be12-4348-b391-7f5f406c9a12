#IAC/modules/eks-addons/sonarqube/variables.tf
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace for SonarQube"
  type        = string
  default     = "sonarqube"
}

variable "chart_version" {
  description = "Version of the SonarQube Helm chart"
  type        = string
  default     = "10.8.1"
}

variable "storage_class" {
  description = "Storage class to use for SonarQube persistent volumes"
  type        = string
  default     = ""
}

variable "storage_size" {
  description = "Size of the SonarQube data volume"
  type        = string
  default     = "20Gi"
}

variable "postgresql_storage_size" {
  description = "Size of the PostgreSQL data volume"
  type        = string
  default     = "25Gi"
}

variable "set_admin_password" {
  description = "Whether to set a custom admin password"
  type        = bool
  default     = false
}

variable "admin_password" {
  description = "Custom admin password to set"
  type        = string
  default     = ""
  sensitive   = true
}

variable "node_selector" {
  description = "Node selector for the SonarQube pod"
  type        = map(string)
  default     = {}
}

variable "resource_requests_cpu" {
  description = "CPU resource requests for SonarQube"
  type        = string
  default     = "400m"
}

variable "resource_requests_memory" {
  description = "Memory resource requests for SonarQube"
  type        = string
  default     = "1024Mi"
}

variable "resource_limits_cpu" {
  description = "CPU resource limits for SonarQube"
  type        = string
  default     = "800m"
}

variable "resource_limits_memory" {
  description = "Memory resource limits for SonarQube"
  type        = string
  default     = "2048Mi"
}

variable "postgres_username" {
  description = "PostgreSQL username for SonarQube"
  type        = string
  default     = "sonar"
}

variable "postgres_database" {
  description = "PostgreSQL database name for SonarQube"
  type        = string
  default     = "sonar"
}

variable "tags" {
  description = "Tags to apply to AWS resources"
  type        = map(string)
  default     = {}
}

# Emissary integration variables
variable "emissary_namespace" {
  description = "Namespace where Emissary is deployed"
  type        = string
  default     = "emissary"
}

variable "sonarqube_hostname" {
  description = "Hostname for SonarQube (leave empty to use any hostname)"
  type        = string
  default     = ""
}

variable "sonarqube_path" {
  description = "Path prefix for SonarQube in the URL"
  type        = string
  default     = "/sonar"
}

variable "tls_secret_name" {
  description = "Name of the TLS secret for HTTPS"
  type        = string
  default     = "emissary-tls"
}
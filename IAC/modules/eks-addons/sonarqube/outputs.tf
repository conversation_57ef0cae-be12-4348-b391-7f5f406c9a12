#IAC/modules/eks-addons/sonarqube/outputs.tf
output "sonarqube_namespace" {
  description = "The Kubernetes namespace where SonarQube is deployed"
  value       = kubernetes_namespace.sonarqube.metadata[0].name
}

output "sonarqube_release_name" {
  description = "The name of the Helm release for SonarQube"
  value       = helm_release.sonarqube.name
}

output "sonarqube_service_name" {
  description = "Name of the SonarQube Kubernetes service"
  value       = "sonarqube-sonarqube"
}

output "sonarqube_url" {
  description = "URL to access SonarQube (without protocol or path)"
  value       = "sonarqube-sonarqube.${kubernetes_namespace.sonarqube.metadata[0].name}.svc.cluster.local"
}

output "monitoring_passcode_secret_arn" {
  description = "ARN of the AWS Secrets Manager secret containing the monitoring passcode"
  value       = aws_secretsmanager_secret.monitoring_passcode.arn
}

output "monitoring_passcode_secret_name" {
  description = "Name of the AWS Secrets Manager secret containing the monitoring passcode"
  value       = aws_secretsmanager_secret.monitoring_passcode.name
}

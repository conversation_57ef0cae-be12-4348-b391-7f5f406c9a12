# IAC/modules/eks-addons/sonarqube/main.tf

# Configure helm to connect to the EKS cluster
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster" {
  name = var.cluster_name
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

# Configure kubectl provider for custom resources
provider "kubectl" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
  load_config_file       = false
}

resource "kubernetes_namespace" "sonarqube" {
  metadata {
    name = var.namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/part-of"    = "sonarqube"
    }
  }
}

# Generate a random admin password with specific character requirements
resource "random_password" "admin_password" {
  length           = 12
  special          = true
  override_special = "$*"
  min_lower        = 2
  min_upper        = 2
  min_numeric      = 2
  min_special      = 2
}

# Store the admin password in AWS Secrets Manager
resource "aws_secretsmanager_secret" "admin_password" {
  name        = "${var.cluster_name}-sonarqube-admin-password"
  description = "SonarQube admin password"
  tags        = var.tags
}

resource "aws_secretsmanager_secret_version" "admin_password" {
  secret_id     = aws_secretsmanager_secret.admin_password.id
  secret_string = random_password.admin_password.result
}

# Generate a random monitoring passcode with specific character requirements
resource "random_password" "monitoring_passcode" {
  length           = 10
  special          = true
  override_special = "$*"
}

# Store the monitoring passcode in AWS Secrets Manager
resource "aws_secretsmanager_secret" "monitoring_passcode" {
  name        = "${var.cluster_name}-sonarqube-monitoring-passcode"
  description = "SonarQube monitoring passcode for liveness checks"
  tags        = var.tags
}

resource "aws_secretsmanager_secret_version" "monitoring_passcode" {
  secret_id     = aws_secretsmanager_secret.monitoring_passcode.id
  secret_string = random_password.monitoring_passcode.result
}

resource "helm_release" "sonarqube" {
  name             = "sonarqube"
  repository       = "https://SonarSource.github.io/helm-chart-sonarqube"
  chart            = "sonarqube"
  version          = var.chart_version
  namespace        = kubernetes_namespace.sonarqube.metadata[0].name
  create_namespace = false
  timeout          = 300
  wait             = true
  
  values = [
    templatefile("${path.module}/values.yaml", {
      storage_class = var.storage_class
      storage_size  = var.storage_size
      node_selector = var.node_selector
      postgresql_storage_size = var.postgresql_storage_size
      resource_requests_cpu = var.resource_requests_cpu
      resource_requests_memory = var.resource_requests_memory
      resource_limits_cpu = var.resource_limits_cpu
      resource_limits_memory = var.resource_limits_memory
      postgresql_username = var.postgres_username
      postgresql_database = var.postgres_database
      sonarqube_path = var.sonarqube_path
    })
  ]

  # Set the monitoring passcode directly from our random generator
  set {
    name  = "monitoringPasscode"
    value = random_password.monitoring_passcode.result
  }
  
  # Set the admin password configuration
  set {
    name  = "setAdminPassword.newPassword"
    value = random_password.admin_password.result
  }
  
  set {
    name  = "setAdminPassword.currentPassword"
    value = "admin"
  }

  depends_on = [
    kubernetes_namespace.sonarqube,
    aws_secretsmanager_secret_version.monitoring_passcode,
    aws_secretsmanager_secret_version.admin_password
  ]
}

# Create Emissary Mapping using kubectl and YAML template
resource "kubectl_manifest" "sonarqube_mapping" {
  yaml_body = templatefile("${path.module}/emissary-mapping.yaml", {
    emissary_namespace = var.emissary_namespace
    hostname_value     = var.sonarqube_hostname != "" ? var.sonarqube_hostname : "*"
    sonarqube_path     = var.sonarqube_path
    sonarqube_namespace = kubernetes_namespace.sonarqube.metadata[0].name
  })

  depends_on = [
    helm_release.sonarqube
  ]
}

# Create Emissary Host using kubectl and YAML template if hostname is specified
resource "kubectl_manifest" "sonarqube_host" {
  count = var.sonarqube_hostname != "" ? 1 : 0
  
  yaml_body = templatefile("${path.module}/emissary-host.yaml", {
    emissary_namespace = var.emissary_namespace
    sonarqube_hostname = var.sonarqube_hostname
    tls_secret_name    = var.tls_secret_name
  })

  depends_on = [
    helm_release.sonarqube
  ]
}
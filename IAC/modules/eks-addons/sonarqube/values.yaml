# IAC/modules/eks-addons/sonarqube/values.yaml
# Use StatefulSet deployment type for better data handling
deploymentType: "StatefulSet"

# Only use 1 replica as multiple instances shouldn't connect to the same database
replicaCount: 1

# Enable this for SonarQube Community Edition
community:
  enabled: true

# Set security context for SonarQube container
containerSecurityContext:
  allowPrivilegeEscalation: false
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 0
  seccompProfile:
    type: RuntimeDefault
  capabilities:
    drop: ["ALL"]

# Configure service
service:
  type: ClusterIP
  externalPort: 9000
  internalPort: 9000

# Configure persistence
persistence:
  enabled: true
  storageClass: "${storage_class}"
  accessMode: ReadWriteOnce
  size: "${storage_size}"

# Configure resource requests and limits
resources:
  limits:
    cpu: "${resource_limits_cpu}"
    memory: "${resource_limits_memory}"
  requests:
    cpu: "${resource_requests_cpu}"
    memory: "${resource_requests_memory}"

# Monitoring passcode is set directly via Helm values using random_password
# The value is also stored in AWS Secrets Manager for reference

# Important: Set SonarQube context to match the URL path in Emissary
env:
  - name: SONAR_WEB_CONTEXT
    value: "${sonarqube_path}"

# Use built-in PostgreSQL
postgresql:
  enabled: true
  postgresqlUsername: "${postgresql_username}"
  postgresqlDatabase: "${postgresql_database}"
  persistence:
    enabled: true
    storageClass: "${storage_class}"
    size: "${postgresql_storage_size}"
  securityContext:
    enabled: true
    fsGroup: 1001
  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    allowPrivilegeEscalation: false
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
  volumePermissions:
    enabled: false
  resources:
    limits:
      cpu: 600m
      memory: 800Mi
    requests:
      cpu: 300m
      memory: 400Mi





# Init containers for SonarQube requirements
initSysctl:
  enabled: true
  vmMaxMapCount: 524288
  fsFileMax: 131072
  nofile: 131072
  nproc: 8192
  securityContext:
    privileged: true
    runAsUser: 0
    readOnlyRootFilesystem: true

# Initialize file system permissions
initFs:
  enabled: true
  securityContext:
    privileged: false
    runAsNonRoot: false
    runAsUser: 0
    runAsGroup: 0
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
      add: ["CHOWN"]
    readOnlyRootFilesystem: true
#IAC/modules/bastion/main.tf
resource "tls_private_key" "bastion_ssh" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "aws_key_pair" "bastion_key" {
  key_name   = var.key_name
  public_key = tls_private_key.bastion_ssh.public_key_openssh
}

resource "aws_secretsmanager_secret" "bastion_key_secret" {
  name = "${var.key_name}_ssh_key"
}

resource "aws_secretsmanager_secret_version" "bastion_key_version" {
  secret_id     = aws_secretsmanager_secret.bastion_key_secret.id
  secret_string = jsonencode({
    private_key = tls_private_key.bastion_ssh.private_key_pem,
    public_key  = tls_private_key.bastion_ssh.public_key_openssh
  })
}

# IAM role for SSM access
resource "aws_iam_role" "bastion_ssm_role" {
  name = "bastion_ssm_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}

# Attach the AmazonSSMManagedInstanceCore policy to the role
resource "aws_iam_role_policy_attachment" "bastion_ssm_policy" {
  role       = aws_iam_role.bastion_ssm_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

# Create an instance profile for the role
resource "aws_iam_instance_profile" "bastion_instance_profile" {
  name = "bastion_instance_profile"
  role = aws_iam_role.bastion_ssm_role.name
}

resource "aws_security_group" "bastion_sg" {
  name        = "bastion_sg"
  description = "Security group for the bastion host"
  vpc_id      = var.vpc_id

  # No ingress rule for SSH since we're using SSM for access
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

data "aws_ssm_parameter" "ubuntu_arm64" {
  name = "/aws/service/canonical/ubuntu/server/24.04/stable/current/arm64/hvm/ebs-gp3/ami-id"
}

locals {
  default_tags = {
    Name = "bastion_host"
    SubComponent = "bastion_host"
  }
}

resource "aws_instance" "bastion_host" {
  ami                    = data.aws_ssm_parameter.ubuntu_arm64.value
  instance_type          = var.instance_type
  key_name               = aws_key_pair.bastion_key.key_name
  subnet_id              = var.private_subnet_id
  vpc_security_group_ids = [aws_security_group.bastion_sg.id]
  iam_instance_profile   = aws_iam_instance_profile.bastion_instance_profile.name
  associate_public_ip_address = false
  
  tags = merge(local.default_tags, var.tags)
}
#IAC/modules/bastion/variables.tf
variable "vpc_id" {
  description = "The VPC ID where the bastion host will be deployed."
  type        = string
}

variable "private_subnet_id" {
  description = "The private subnet ID where the bastion host will be deployed."
  type        = string
}

variable "key_name" {
  description = "The name of the SSH key pair."
  type        = string
  default = "bastion_key"
}

variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "instance_type" {
  description = "Instance type of bastion host"
  type        = string
  default     = "t4g.small"
}
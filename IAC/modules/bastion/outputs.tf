#IAC/modules/bastion/outputs.tf
output "bastion_host_private_ip" {
  description = "The private IP address of the bastion host."
  value       = aws_instance.bastion_host.private_ip
}

output "bastion_host_instance_id" {
  description = "The instance ID of the bastion host."
  value       = aws_instance.bastion_host.id
}

output "bastion_ssh_key_secret_arn" {
  description = "The ARN of the SSH key stored in AWS Secrets Manager."
  value       = aws_secretsmanager_secret.bastion_key_secret.arn
}

output "bastion_sg_id" {
  value       = aws_security_group.bastion_sg.id
  description = "The security group ID of the bastion host"
}

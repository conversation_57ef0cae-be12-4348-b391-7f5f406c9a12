# IAC_BASE/modules/eks-msk-irsa/outputs.tf

output "role_arn" {
  description = "ARN of the IAM role created for MSK access"
  value       = aws_iam_role.eks_msk_irsa_role.arn
}

output "role_name" {
  description = "Name of the IAM role created for MSK access"
  value       = aws_iam_role.eks_msk_irsa_role.name
}

output "policy_arn" {
  description = "ARN of the policy attached to the role (created or existing)"
  value       = var.create_policy ? aws_iam_policy.msk_iam_access[0].arn : var.existing_policy_arn
} 
# Create an IAM role for the Kubernetes service account
resource "aws_iam_role" "eks_msk_irsa_role" {
  name = "${var.environment}-eks-msk-irsa-role"
  
  # Trust relationship to allow the EKS service accounts to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${var.oidc_provider}"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "${var.oidc_provider}:aud": "sts.amazonaws.com"
          },
          StringLike = {
            "${var.oidc_provider}:sub": flatten([
              for namespace, service_accounts in var.service_accounts : [
                for sa in service_accounts : "system:serviceaccount:${namespace}:${sa}"
              ]
            ])
          }
        }
      }
    ]
  })
  
  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-eks-msk-role"
    }
  )
}

# Extract cluster name and UUID from the cluster ARN for correct resource formatting
locals {
  cluster_parts = split("/", var.msk_cluster_arn)
  cluster_name = local.cluster_parts[1]
  cluster_uuid = length(local.cluster_parts) > 2 ? local.cluster_parts[2] : ""
}

# Create or use a policy to grant MSK access with IAM authentication
resource "aws_iam_policy" "msk_iam_access" {
  count       = var.create_policy ? 1 : 0
  name        = "${var.environment}-eks-msk-access-policy"
  description = "IAM policy for MSK access with IAM authentication"
  
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "kafka-cluster:Connect",
          "kafka-cluster:DescribeCluster"
        ],
        Resource = var.msk_cluster_arn
      },
      {
        Effect = "Allow",
        Action = [
          "kafka-cluster:*Topic*",
          "kafka-cluster:WriteData",
          "kafka-cluster:ReadData"
        ],
        Resource = "arn:aws:kafka:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:topic/${local.cluster_name}/${local.cluster_uuid}/*"
      },
      {
        Effect = "Allow",
        Action = [
          "kafka-cluster:AlterGroup",
          "kafka-cluster:DescribeGroup"
        ],
        Resource = "arn:aws:kafka:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:group/${local.cluster_name}/${local.cluster_uuid}/*"
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "msk_policy_attachment" {
  role       = aws_iam_role.eks_msk_irsa_role.name
  policy_arn = var.create_policy ? aws_iam_policy.msk_iam_access[0].arn : var.existing_policy_arn
}

# Get the current AWS account ID and region
data "aws_caller_identity" "current" {}
data "aws_region" "current" {} 
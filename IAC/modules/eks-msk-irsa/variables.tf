# IAC_BASE/modules/eks-msk-irsa/variables.tf

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "oidc_provider" {
  description = "OIDC provider URL for the EKS cluster (without https://)"
  type        = string
}

variable "msk_cluster_arn" {
  description = "ARN of the MSK cluster"
  type        = string
}

variable "create_policy" {
  description = "Whether to create a new policy or use an existing one"
  type        = bool
  default     = true
}

variable "existing_policy_arn" {
  description = "ARN of an existing MSK access policy if not creating a new one"
  type        = string
  default     = ""
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
} 

variable "service_accounts" {
  description = "Map of namespace to list of service accounts that can assume this role"
  type        = map(list(string))
  default     = {}
  # Example: 
  # {
  #   "microservices" = ["microservices-sa"]
  #   "ambient-testing" = ["ambient-testing-sa", "another-sa"]
  # }
}
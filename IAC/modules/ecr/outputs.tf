#IAC/modules/ecr/outputs.tf
output "repository_urls" {
  description = "Map of repository names to their registry URLs"
  value       = { for name, repo in aws_ecr_repository.repositories : name => repo.repository_url }
}

output "repository_arns" {
  description = "Map of repository names to their ARNs"
  value       = { for name, repo in aws_ecr_repository.repositories : name => repo.arn }
}

output "repository_registry_ids" {
  description = "Map of repository names to their registry IDs"
  value       = { for name, repo in aws_ecr_repository.repositories : name => repo.registry_id }
}

output "ecr_role_arn" {
  description = "ARN of the ECR access IAM role (if created)"
  value       = var.create_access_role ? aws_iam_role.ecr_role[0].arn : null
}

output "ecr_role_name" {
  description = "Name of the ECR access IAM role (if created)"
  value       = var.create_access_role ? aws_iam_role.ecr_role[0].name : null
}

output "ecr_policy_arn" {
  description = "ARN of the ECR access IAM policy (if created)"
  value       = var.create_access_role ? aws_iam_policy.ecr_policy[0].arn : null
}

output "replication_configuration_id" {
  description = "ID of the ECR replication configuration (if created)"
  value       = var.enable_replication ? aws_ecr_replication_configuration.replication[0].id : null
}
 #IAC/modules/ecr/main.tf
resource "aws_ecr_repository" "repositories" {
  for_each = toset(var.repository_names)
  
  name                 = each.value
  image_tag_mutability = lookup(var.repository_configs, each.value, null) != null ? var.repository_configs[each.value].image_tag_mutability : var.default_image_tag_mutability
  force_delete        = var.force_delete

  image_scanning_configuration {
    scan_on_push = lookup(var.repository_configs, each.value, null) != null ? var.repository_configs[each.value].scan_on_push : var.default_scan_on_push
  }

  encryption_configuration {
    encryption_type = lookup(var.repository_configs, each.value, null) != null ? var.repository_configs[each.value].encryption_type : var.default_encryption_type
    kms_key         = lookup(var.repository_configs, each.value, null) != null ? lookup(var.repository_configs[each.value], "kms_key_id", null) : var.kms_key_id
  }

  tags = merge(var.tags, {
    Name = each.value
  })
}

resource "aws_ecr_lifecycle_policy" "policy" {
  for_each = var.enable_lifecycle_policy ? toset(var.repository_names) : []
  
  repository = aws_ecr_repository.repositories[each.value].name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last ${var.max_image_count} production images"
        selection = {
          tagStatus     = "tagged"
          tagPrefixList = ["prod", "production"]
          countType     = "imageCountMoreThan"
          countNumber   = var.max_image_count_prod
        }
        action = {
          type = "expire"
        }
      },
      {
        rulePriority = 2
        description  = "Keep last ${var.max_image_count} images"
        selection = {
          tagStatus   = "tagged"
          countType   = "imageCountMoreThan"
          countNumber = var.max_image_count
        }
        action = {
          type = "expire"
        }
      },
      {
        rulePriority = 3
        description  = "Delete untagged images older than ${var.untagged_expire_days} days"
        selection = {
          tagStatus   = "untagged"
          countType   = "sinceImagePushed"
          countUnit   = "days"
          countNumber = var.untagged_expire_days
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# Create IAM role for ECR access
resource "aws_iam_role" "ecr_role" {
  count = var.create_access_role ? 1 : 0
  
  name = "${var.environment}-ecr-access-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          AWS = var.trusted_accounts
        }
      },
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# ECR access policy
resource "aws_iam_policy" "ecr_policy" {
  count = var.create_access_role ? 1 : 0
  
  name        = "${var.environment}-ecr-access-policy"
  description = "IAM policy for ECR access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:DescribeRepositories",
          "ecr:DescribeImages",
          "ecr:ListImages"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:PutImage"
        ]
        Resource = [for repo in aws_ecr_repository.repositories : repo.arn]
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "ecr_policy_attachment" {
  count = var.create_access_role ? 1 : 0
  
  role       = aws_iam_role.ecr_role[0].name
  policy_arn = aws_iam_policy.ecr_policy[0].arn
}

# Create repository policy for cross-account access
resource "aws_ecr_repository_policy" "repository_policy" {
  for_each = var.enable_cross_account_access ? toset(var.repository_names) : []
  
  repository = aws_ecr_repository.repositories[each.value].name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowCrossAccountAccess"
        Effect = "Allow"
        Principal = {
          AWS = var.trusted_accounts
        }
        Action = [
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability",
          "ecr:PutImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:DescribeRepositories",
          "ecr:GetRepositoryPolicy",
          "ecr:ListImages",
          "ecr:DescribeImages"
        ]
      }
    ]
  })
}

# ECR Replication Configuration
resource "aws_ecr_replication_configuration" "replication" {
  count = var.enable_replication ? 1 : 0

  replication_configuration {
    dynamic "rule" {
      for_each = var.replication_rules
      content {
        dynamic "destination" {
          for_each = rule.value.destinations
          content {
            region      = destination.value.region
            registry_id = destination.value.registry_id
          }
        }
        
        dynamic "repository_filter" {
          for_each = rule.value.repository_filters
          content {
            filter      = repository_filter.value.filter
            filter_type = repository_filter.value.filter_type
          }
        }
      }
    }
  }
}
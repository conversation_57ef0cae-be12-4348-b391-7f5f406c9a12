#IAC/modules/ecr/variables.tf
variable "repository_names" {
  description = "List of ECR repository names to create"
  type        = list(string)
  validation {
    condition     = length(var.repository_names) > 0
    error_message = "At least one repository name must be provided."
  }
}

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "repository_configs" {
  description = "Repository-specific configurations"
  type = map(object({
    image_tag_mutability = string
    scan_on_push         = bool
    encryption_type      = string
    kms_key_id          = optional(string)
  }))
  default = {}
}

variable "default_image_tag_mutability" {
  description = "Default tag mutability setting for repositories not specified in repository_configs"
  type        = string
  default     = "MUTABLE"
  validation {
    condition     = contains(["MUTABLE", "IMMUTABLE"], var.default_image_tag_mutability)
    error_message = "Image tag mutability must be either MUTABLE or IMMUTABLE."
  }
}

variable "default_scan_on_push" {
  description = "Default scan on push setting for repositories not specified in repository_configs"
  type        = bool
  default     = true
}

variable "default_encryption_type" {
  description = "Default encryption type for repositories not specified in repository_configs"
  type        = string
  default     = "AES256"
  validation {
    condition     = contains(["AES256", "KMS"], var.default_encryption_type)
    error_message = "Encryption type must be either AES256 or KMS."
  }
}

variable "encryption_type" {
  description = "The encryption type to use for the repository. Valid values: AES256, KMS"
  type        = string
  default     = "AES256"
  validation {
    condition     = contains(["AES256", "KMS"], var.encryption_type)
    error_message = "Encryption type must be either AES256 or KMS."
  }
}

variable "kms_key_id" {
  description = "The KMS key ID to use for encryption when encryption_type is KMS"
  type        = string
  default     = null
}

variable "enable_lifecycle_policy" {
  description = "Whether to enable lifecycle policy for repositories"
  type        = bool
  default     = true
}

variable "max_image_count" {
  description = "Maximum number of images to keep (for non-production images)"
  type        = number
  default     = 10
}

variable "max_image_count_prod" {
  description = "Maximum number of production images to keep"
  type        = number
  default     = 5
}

variable "untagged_expire_days" {
  description = "Number of days after which untagged images expire"
  type        = number
  default     = 7
}

variable "create_access_role" {
  description = "Whether to create IAM role for ECR access"
  type        = bool
  default     = false
}

variable "trusted_accounts" {
  description = "List of AWS account IDs that are trusted for cross-account access"
  type        = list(string)
  default     = []
}

variable "enable_cross_account_access" {
  description = "Whether to enable cross-account access policies"
  type        = bool
  default     = false
}

variable "enable_replication" {
  description = "Whether to enable ECR replication"
  type        = bool
  default     = false
}

variable "replication_rules" {
  description = "List of replication rules with destinations and repository filters"
  type = list(object({
    destinations = list(object({
      region      = string
      registry_id = string
    }))
    repository_filters = list(object({
      filter      = string
      filter_type = string
    }))
  }))
  default = []
}

variable "force_delete" {
  description = "If true, will delete the repository even if it contains images"
  type        = bool
  default     = false
}

variable "image_tag_immutability" {
  description = "The tag mutability setting for the repository"
  type        = string
  default     = "MUTABLE"
  validation {
    condition     = contains(["MUTABLE", "IMMUTABLE"], var.image_tag_immutability)
    error_message = "Image tag immutability must be either MUTABLE or IMMUTABLE."
  }
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
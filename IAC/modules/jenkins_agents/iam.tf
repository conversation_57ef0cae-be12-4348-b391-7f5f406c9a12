# IAM role for Jenkins agent instances
#IAC/modules/jenkins_agents/iam.tf
resource "aws_iam_role" "jenkins_agent" {
  name = "${var.name_prefix}-jenkins-agent-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Instance profile for the IAM role
resource "aws_iam_instance_profile" "jenkins_agent" {
  name = "${var.name_prefix}-jenkins-agent-profile"
  role = aws_iam_role.jenkins_agent.name
}

# Basic policy for Jenkins agents
resource "aws_iam_policy" "jenkins_agent_basic" {
  name        = "${var.name_prefix}-jenkins-agent-basic-policy"
  description = "Basic policy for Jenkins agent operations"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:DescribeInstances",
          "ec2:DescribeTags"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:PutImage"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach SSM policy for Session Manager access
resource "aws_iam_role_policy_attachment" "jenkins_agent_ssm" {
  role       = aws_iam_role.jenkins_agent.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

# Attach basic policy to the Jenkins agent role
resource "aws_iam_role_policy_attachment" "jenkins_agent_basic" {
  role       = aws_iam_role.jenkins_agent.name
  policy_arn = aws_iam_policy.jenkins_agent_basic.arn
}

# CloudWatch policy removed for ephemeral agents to reduce overhead
# Environment and Account Configuration
#IAC/modules/jenkins_agents/variables.tf
variable "name_prefix" {
  description = "Prefix to use for resource names"
  type        = string
}

variable "aws_region" {
  description = "AWS region where resources will be created"
  type        = string
}

variable "account_id" {
  description = "AWS account ID where resources will be created"
  type        = string
}

# Network Configuration
variable "vpc_id" {
  description = "ID of the VPC where Jenkins agent will be deployed"
  type        = string
}

variable "subnet_ids" {
  description = "List of private subnet IDs where Jenkins agent instances will be deployed"
  type        = list(string)
}

# Jenkins Master Configuration
variable "jenkins_master_sg_id" {
  description = "Security group ID of the Jenkins master"
  type        = string
}

variable "jenkins_agent_public_key" {
  description = "Public key content for SSH authentication with Jenkins master"
  type        = string
}

# Instance Configuration
variable "agent_ami_id" {
  description = "AMI ID for Jenkins agent instances"
  type        = string
}

variable "instance_type" {
  description = "EC2 instance type for Jenkins agents"
  type        = string
  default     = "t3.medium"
}

variable "root_volume_size" {
  description = "Size in GB for Jenkins agent root EBS volume"
  type        = number
  default     = 30
  validation {
    condition     = var.root_volume_size >= 20
    error_message = "Root volume size must be at least 20 GB."
  }
}

variable "spot_max_price" {
  description = "Maximum spot price for agent instances (use \"\" for on-demand price)"
  type        = string
  default     = ""  # Use empty string for on-demand price
}

# Auto Scaling Group Configuration
variable "min_size" {
  description = "Minimum size of the agent ASG"
  type        = number
  default     = 0
}

variable "max_size" {
  description = "Maximum size of the agent ASG"
  type        = number
  default     = 10
}

variable "desired_capacity" {
  description = "Desired capacity of the agent ASG"
  type        = number
  default     = 0
}

# Agent Script Configuration
variable "agent_userdata_script_version" {
  description = "Version of the agent user data script"
  type        = string
  default     = "0.1"
}

# Additional Tags
variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# Add these new variables to your existing variables.tf file

variable "additional_instance_types" {
  description = "List of additional EC2 instance types to use in the mixed instances policy"
  type        = list(string)
  default     = ["t3a.medium", "t2.medium"]  # AMD variant and previous generation as alternatives
}

variable "on_demand_percentage" {
  description = "Percentage of on-demand instances to use in the fleet"
  type        = number
  default     = 0  # Default to all spot instances
  validation {
    condition     = var.on_demand_percentage >= 0 && var.on_demand_percentage <= 100
    error_message = "On-demand percentage must be between 0 and 100."
  }
}

variable "bitbucket_private_key" {
  description = "BitBucket private key for SSH authentication"
  type        = string
  default     = ""  # Empty default for optional usage
  sensitive   = true
}
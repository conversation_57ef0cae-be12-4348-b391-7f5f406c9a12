# Jenkins Agent Module - Main Configuration
# IAC/modules/jenkins_agents/main.tf
terraform {
  required_version = ">= 1.0.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.0.0"
    }
  }
}

# Launch Template for Jenkins Agents
resource "aws_launch_template" "jenkins_agent" {
  name_prefix   = "${var.name_prefix}-jenkins-agent-"
  image_id      = var.agent_ami_id
  
  # We're removing the instance_type from here because it will be specified
  # in the mixed instances policy of the ASG

  # EBS volume configuration
  block_device_mappings {
    device_name = "/dev/sda1"
    ebs {
      volume_size           = var.root_volume_size
      volume_type           = "gp3"
      delete_on_termination = true
      encrypted             = true
    }
  }

  # Attach the IAM instance profile
  iam_instance_profile {
    name = aws_iam_instance_profile.jenkins_agent.name
  }

  # Network configuration - instances in private subnets
  network_interfaces {
    associate_public_ip_address = false
    security_groups             = [aws_security_group.jenkins_agent.id]
  }

  # User data script for agent configuration
  user_data = base64encode(templatefile("${path.module}/templates/agent_user_data.sh", {
    jenkins_agent_public_key = var.jenkins_agent_public_key
    script_version           = var.agent_userdata_script_version
  }))

  # We're removing the spot configuration from here as it will be handled
  # by the mixed instances policy in the ASG

  # Tags for EC2 instances
  tag_specifications {
    resource_type = "instance"
    tags = merge(
      {
        Name = "${var.name_prefix}-jenkins-agent"
        SubComponent = "agent-ec2-instance"
      },
      var.tags
    )
  }

  # Tags for EBS volumes
  tag_specifications {
    resource_type = "volume"
    tags = merge(
      {
        Name = "${var.name_prefix}-jenkins-agent-volume"
        SubComponent = "agent-ebs-volume"
      },
      var.tags
    )
  }

  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"  # Enforce IMDSv2
    http_put_response_hop_limit = 1
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Auto Scaling Group for Jenkins Agents - Updated for EC2 Fleet plugin
resource "aws_autoscaling_group" "jenkins_agent" {
  name                = "${var.name_prefix}-jenkins-agent-fleet"  # Renamed to clearly identify as the fleet
  min_size            = 0  # EC2 Fleet plugin will manage this
  max_size            = var.max_size
  desired_capacity    = 0  # EC2 Fleet plugin will manage this
  vpc_zone_identifier = var.subnet_ids
  health_check_type   = "EC2"
  health_check_grace_period = 300

  # Use mixed instances policy for better spot availability and cost optimization
  mixed_instances_policy {
    instances_distribution {
      on_demand_base_capacity                  = 0
      on_demand_percentage_above_base_capacity = var.on_demand_percentage
      spot_allocation_strategy                 = "capacity-optimized"  # Best practice for availability
    }
    
    launch_template {
      launch_template_specification {
        launch_template_id = aws_launch_template.jenkins_agent.id
        version            = "$Latest"
      }
      
      # Primary instance type
      override {
        instance_type = var.instance_type
      }
      
      # Additional similar instance types for better spot availability
      dynamic "override" {
        for_each = var.additional_instance_types
        content {
          instance_type = override.value
        }
      }
    }
  }

  # ASG instance refresh policy
  instance_refresh {
    strategy = "Rolling"
    preferences {
      min_healthy_percentage = 50
    }
  }

  # EC2 Fleet Plugin specific tag - required for the plugin to identify instances
  tag {
    key                 = "ec2-fleet-plugin:cloud-name"
    value               = "${var.name_prefix}-jenkins-agent-fleet" #this should match the name of the EC2 fleet cloud else dynamic provisioning wont work
    propagate_at_launch = true
  }
  
  # Regular tags
  dynamic "tag" {
    for_each = merge(
      {
        Name = "${var.name_prefix}-jenkins-agent"
      },
      var.tags
    )
    content {
      key                 = tag.key
      value               = tag.value
      propagate_at_launch = true
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Original security group configuration - unchanged
resource "aws_security_group" "jenkins_agent" {
  name        = "${var.name_prefix}-jenkins-agent-sg"
  description = "Security group for Jenkins agent instances"
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    {
      Name = "${var.name_prefix}-jenkins-agent-sg"
      SubComponent = "security-group"
    },
    var.tags
  )
}

# Allow SSH from Jenkins master to agents - unchanged
resource "aws_security_group_rule" "jenkins_agent_ssh" {
  type                     = "ingress"
  from_port                = 22
  to_port                  = 22
  protocol                 = "tcp"
  security_group_id        = aws_security_group.jenkins_agent.id
  source_security_group_id = var.jenkins_master_sg_id
  description              = "Allow SSH from Jenkins master"
}
#IAC/modules/jenkins_agents/outputs.tf
output "asg_name" {
  description = "Name of the Jenkins Agent Auto Scaling Group"
  value       = aws_autoscaling_group.jenkins_agent.name
}

output "launch_template_id" {
  description = "ID of the Jenkins Agent Launch Template"
  value       = aws_launch_template.jenkins_agent.id
}

output "security_group_id" {
  description = "ID of the Jenkins Agent Security Group"
  value       = aws_security_group.jenkins_agent.id
}

output "iam_role_name" {
  description = "Name of the Jenkins Agent IAM Role"
  value       = aws_iam_role.jenkins_agent.name
}
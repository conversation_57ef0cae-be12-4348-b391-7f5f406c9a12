#!/bin/bash
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log_message "Starting Jenkins agent setup script version: ${script_version}"

sudo apt-get install yarn -y

# Ensure Docker is running and ubuntu user can use it
log_message "Ensuring Docker service is running"
if systemctl is-active --quiet docker; then
    log_message "Docker service is already running"
else
    log_message "Starting Docker service"
    sudo systemctl start docker
    sudo systemctl enable docker
fi

# Make sure ubuntu user is in docker group
if ! groups ubuntu | grep -q docker; then
    log_message "Adding ubuntu user to docker group"
    sudo usermod -aG docker ubuntu
fi


# Print summary information
log_message "Jenkins agent setup completed."
log_message "- Docker service status: $(systemctl is-active docker)"
log_message "- Node.js version: $(node --version 2>/dev/null || echo 'Not installed')"
log_message "- Java version: $(java -version 2>&1 | head -n 1 || echo 'Not installed')"
log_message "- Dependency Check: $(/home/<USER>/dependency-check/bin/dependency-check.sh --version 2>&1 || echo 'Not installed')"
log_message "- Trivy version: $(trivy --version 2>&1 | head -n 1 || echo 'Not installed')"
log_message "- Working directory: /home/<USER>/workspace"
log_message "- Environment setup script: /home/<USER>/setup-jenkins-env.sh"
log_message "- Ubuntu user can connect via SSH from Jenkins master"
log_message "- SSH known_hosts configured for GitHub, GitLab, and BitBucket"
log_message "Setup script completed successfully."
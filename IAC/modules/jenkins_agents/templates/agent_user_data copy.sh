 #!/bin/bash
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log_message "Starting Jenkins agent setup script version: ${script_version}"

# Create jenkins user (in case it doesn't exist)
if ! id -u jenkins &>/dev/null; then
    log_message "Creating jenkins user"
    sudo adduser --system --home /var/lib/jenkins --shell /bin/bash --group --disabled-password jenkins
fi

# Setup SSH directory for ubuntu user
log_message "Setting up SSH for ubuntu user"
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# # Add Jenkins master's public key to authorized_keys
# log_message "Adding Jenkins master public key to authorized_keys"
# echo "${jenkins_agent_public_key}" | tee -a /home/<USER>/.ssh/authorized_keys > /dev/null
# chmod 600 /home/<USER>/.ssh/authorized_keys

# # Setup known_hosts file for ubuntu user
# log_message "Setting up known_hosts file for ubuntu user..."
# touch /home/<USER>/.ssh/known_hosts

# # Add common Git hosts to known_hosts
# for HOST in "github.com" "gitlab.com" "bitbucket.org"; do
#     log_message "Adding $HOST to known_hosts..."
#     ssh-keyscan -t rsa $HOST >> /home/<USER>/.ssh/known_hosts
# done

# chmod 644 /home/<USER>/.ssh/known_hosts

# # Setup BitBucket private key if provided
# if [ -n "${bitbucket_private_key}" ]; then
#     log_message "Setting up BitBucket private key for SSH authentication"
#     echo "${bitbucket_private_key}" > /home/<USER>/.ssh/bitbucket_id_rsa
#     chmod 600 /home/<USER>/.ssh/bitbucket_id_rsa
    
#     # Configure SSH to use this key for BitBucket
#     cat > /home/<USER>/.ssh/config << EOF
# Host bitbucket.org
#     IdentityFile /home/<USER>/.ssh/bitbucket_id_rsa
#     StrictHostKeyChecking no
# EOF
#     chmod 600 /home/<USER>/.ssh/config
# fi

chown -R ubuntu:ubuntu /home/<USER>/.ssh

# Ensure Docker is running and ubuntu user can use it
log_message "Ensuring Docker service is running"
if systemctl is-active --quiet docker; then
    log_message "Docker service is already running"
else
    log_message "Starting Docker service"
    sudo systemctl start docker
    sudo systemctl enable docker
fi

# Make sure ubuntu user is in docker group
if ! groups ubuntu | grep -q docker; then
    log_message "Adding ubuntu user to docker group"
    sudo usermod -aG docker ubuntu
fi

# Set up proper permissions and environment for security scanning tools
# Configure OWASP Dependency Check
if [ -d /home/<USER>/dependency-check ]; then
    log_message "Setting up OWASP Dependency Check"
    chmod -R 755 /home/<USER>/dependency-check
    chown -R ubuntu:ubuntu /home/<USER>/dependency-check
    
    # Create a symlink for easier access from any location
    if [ ! -L /usr/local/bin/dependency-check ]; then
        sudo ln -s /home/<USER>/dependency-check/bin/dependency-check.sh /usr/local/bin/dependency-check
        sudo chmod +x /usr/local/bin/dependency-check
    fi
    
    # Verify dependency-check is working
    log_message "Dependency Check version: $(/home/<USER>/dependency-check/bin/dependency-check.sh --version 2>&1 || echo 'Not working properly')"
fi

# Configure Trivy
if command -v trivy &> /dev/null; then
    log_message "Setting up Trivy scanner"
    
    # Ensure Trivy DB is updated
    sudo -u ubuntu trivy --quiet image --download-db-only
    
    # Verify Trivy is working
    log_message "Trivy version: $(trivy --version 2>&1 | head -n 1 || echo 'Not working properly')"
else
    log_message "WARNING: Trivy not found in PATH"
fi

# Check PATH for required tools
log_message "Verifying tools in PATH"
for tool in java node npm docker trivy aws; do
    if command -v $tool &> /dev/null; then
        log_message "$tool found: $(command -v $tool)"
    else
        log_message "WARNING: $tool not found in PATH"
    fi
done

# Create workspace directory
log_message "Creating workspace directory"
mkdir -p /home/<USER>/workspace
chown ubuntu:ubuntu /home/<USER>/workspace
chmod 755 /home/<USER>/workspace

# Create a script to set up environment for Jenkins jobs
cat > /home/<USER>/setup-jenkins-env.sh << 'EOL'
#!/bin/bash
# This script sets up the environment for Jenkins jobs

# Add security tools to PATH
export PATH=$PATH:/home/<USER>/dependency-check/bin:/usr/local/bin

# Add any other environment variables needed for builds
export DEPENDENCY_CHECK_HOME=/home/<USER>/dependency-check
export WORKSPACE=$${WORKSPACE:-/home/<USER>/workspace}

# Display tool versions for debugging
echo "=== Build Environment ==="
echo "Node.js: $(node --version 2>/dev/null || echo 'Not installed')"
echo "NPM: $(npm --version 2>/dev/null || echo 'Not installed')"
echo "Java: $(java -version 2>&1 | head -n 1 || echo 'Not installed')"
echo "Docker: $(docker --version 2>/dev/null || echo 'Not installed')"
echo "Trivy: $(trivy --version 2>/dev/null | head -n 1 || echo 'Not installed')"
echo "Dependency Check: $(/home/<USER>/dependency-check/bin/dependency-check.sh --version 2>/dev/null || echo 'Not installed')"
echo "AWS CLI: $(aws --version 2>/dev/null || echo 'Not installed')"
echo "======================="
EOL

chmod +x /home/<USER>/setup-jenkins-env.sh
chown ubuntu:ubuntu /home/<USER>/setup-jenkins-env.sh

# Print summary information
log_message "Jenkins agent setup completed."
log_message "- Docker service status: $(systemctl is-active docker)"
log_message "- Node.js version: $(node --version 2>/dev/null || echo 'Not installed')"
log_message "- Java version: $(java -version 2>&1 | head -n 1 || echo 'Not installed')"
log_message "- Dependency Check: $(/home/<USER>/dependency-check/bin/dependency-check.sh --version 2>&1 || echo 'Not installed')"
log_message "- Trivy version: $(trivy --version 2>&1 | head -n 1 || echo 'Not installed')"
log_message "- Working directory: /home/<USER>/workspace"
log_message "- Environment setup script: /home/<USER>/setup-jenkins-env.sh"
log_message "- Ubuntu user can connect via SSH from Jenkins master"
log_message "- SSH known_hosts configured for GitHub, GitLab, and BitBucket"
# if [ -n "${bitbucket_private_key}" ]; then
#     log_message "- BitBucket private key configured for SSH authentication"
# fi

log_message "Setup script completed successfully."
variable "environment" {
  description = "Environment name"
  type        = string
}

variable "cluster_name" {
  description = "The name of the EKS cluster"
  type        = string
}

variable "vpc_id" {
  description = "The ID of the VPC where the MSK cluster will be created"
  type        = string
}

variable "subnet_ids" {
  description = "The IDs of the subnets where the MSK brokers will be placed"
  type        = list(string)
}

variable "eks_node_security_group_id" {
  description = "The ID of the EKS node security group"
  type        = string
}

variable "kafka_version" {
  description = "The version of Kafka for the MSK cluster"
  type        = string
  default     = "3.6.0"
}

variable "broker_count" {
  description = "The number of broker nodes in the cluster"
  type        = number
  default     = 2
}

variable "broker_instance_type" {
  description = "The instance type to use for the Kafka brokers"
  type        = string
  default     = "kafka.m7g.large"
}

variable "broker_volume_size" {
  description = "The size in GiB of the EBS volume for the data drive on each broker node"
  type        = number
  default     = 40
}

variable "num_partitions" {
  description = "Default number of partitions per topic"
  type        = number
  default     = 3
}

variable "kms_deletion_window_in_days" {
  description = "Duration in days after which the key is deleted after destruction of the resource"
  type        = number
  default     = 30
}

variable "kms_enable_key_rotation" {
  description = "Specifies whether key rotation is enabled"
  type        = bool
  default     = true
}

variable "kafka_topics" {
  description = "Map of Kafka topics to create with their configurations"
  type = map(object({
    partitions         = optional(number)
    replication_factor = optional(number)
    config             = optional(map(string))
  }))
  default = {}
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
} 
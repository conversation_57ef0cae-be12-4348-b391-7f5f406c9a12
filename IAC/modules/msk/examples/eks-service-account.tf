# Example of how to set up IRSA for a Kubernetes application to access MSK
# This would typically be in an application-specific module

# Get the OIDC provider URL from the EKS cluster
data "aws_eks_cluster" "cluster" {
  name = var.cluster_name
}

data "aws_caller_identity" "current" {}

locals {
  oidc_provider = replace(data.aws_eks_cluster.cluster.identity[0].oidc[0].issuer, "https://", "")
}

# Create an IAM role for the Kubernetes service account
resource "aws_iam_role" "kafka_app_role" {
  name = "eks-kafka-app-${var.environment}"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${local.oidc_provider}"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "${local.oidc_provider}:sub": "system:serviceaccount:${var.app_namespace}:${var.app_service_account}"
          }
        }
      }
    ]
  })

  tags = {
    Environment = var.environment
    Application = var.app_name
  }
}

# Attach the MSK access policy to the role
resource "aws_iam_role_policy_attachment" "kafka_policy_attachment" {
  role       = aws_iam_role.kafka_app_role.name
  policy_arn = var.msk_access_policy_arn  # This would be the output from your MSK module
}

# Create the Kubernetes service account
resource "kubernetes_service_account" "kafka_app_sa" {
  metadata {
    name      = var.app_service_account
    namespace = var.app_namespace
    annotations = {
      "eks.amazonaws.com/role-arn" = aws_iam_role.kafka_app_role.arn
    }
  }
}

# Example variables for this module
variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "app_namespace" {
  description = "Kubernetes namespace for the application"
  type        = string
}

variable "app_service_account" {
  description = "Name of the Kubernetes service account"
  type        = string
}

variable "msk_access_policy_arn" {
  description = "ARN of the MSK access policy"
  type        = string
} 
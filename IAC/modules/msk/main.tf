resource "aws_security_group" "msk_sg" {
  name        = "${var.environment}-msk-sg"
  description = "Security group for MSK cluster"
  vpc_id      = var.vpc_id

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-msk-sg"
    }
  )
}

resource "aws_security_group_rule" "msk_eks_ingress" {
  security_group_id        = aws_security_group.msk_sg.id
  type                     = "ingress"
  from_port                = 9092
  to_port                  = 9098
  protocol                 = "tcp"
  source_security_group_id = var.eks_node_security_group_id
  description              = "Allow EKS nodes to communicate with MSK cluster"
}

resource "aws_security_group_rule" "msk_self_communication" {
  security_group_id = aws_security_group.msk_sg.id
  type              = "ingress"
  from_port         = 0
  to_port           = 65535
  protocol          = "tcp"
  self              = true
  description       = "Allow MSK nodes to communicate with each other"
}

resource "aws_security_group_rule" "msk_outbound" {
  security_group_id = aws_security_group.msk_sg.id
  type              = "egress"
  from_port         = 0
  to_port           = 65535
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  description       = "Allow MSK outbound traffic"
}

resource "aws_kms_key" "msk_kms" {
  description             = "KMS key for MSK cluster encryption"
  deletion_window_in_days = var.kms_deletion_window_in_days
  enable_key_rotation     = var.kms_enable_key_rotation

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-msk-kms"
    }
  )
}

resource "aws_cloudwatch_log_group" "msk_log_group" {
  name              = "/aws/msk/${var.cluster_name}"
  retention_in_days = 7

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-msk-logs"
    }
  )
}

resource "aws_msk_cluster" "kafka_cluster" {
  cluster_name           = "${var.environment}-msk"
  kafka_version          = var.kafka_version
  number_of_broker_nodes = var.broker_count

  broker_node_group_info {
    instance_type   = var.broker_instance_type
    client_subnets  = var.subnet_ids
    security_groups = [aws_security_group.msk_sg.id]

    storage_info {
      ebs_storage_info {
        volume_size = var.broker_volume_size
      }
    }
  }

  encryption_info {
    encryption_in_transit {
      client_broker = "TLS"
      in_cluster    = true
    }
    encryption_at_rest_kms_key_arn = aws_kms_key.msk_kms.arn
  }

  client_authentication {
    sasl {
      iam = true
    }
  }
  

  open_monitoring {
    prometheus {
      jmx_exporter {
        enabled_in_broker = true
      }
      node_exporter {
        enabled_in_broker = true
      }
    }
  }

  logging_info {
    broker_logs {
      cloudwatch_logs {
        enabled   = true
        log_group = aws_cloudwatch_log_group.msk_log_group.name
      }
    }
  }

  configuration_info {
    arn      = aws_msk_configuration.msk_config.arn
    revision = aws_msk_configuration.msk_config.latest_revision
  }

  tags = merge(
    var.tags,
    {
      Name        = "${var.environment}-msk"
      Environment = var.environment
    }
  )
}

resource "aws_msk_configuration" "msk_config" {
  name              = "${var.environment}-msk-config"
  kafka_versions    = [var.kafka_version]
  server_properties = <<PROPERTIES
num.partitions=${var.num_partitions}
default.replication.factor=${var.broker_count > 2 ? 3 : 2}
min.insync.replicas=${var.broker_count > 2 ? 2 : 1}
auto.create.topics.enable=true
delete.topic.enable=true
PROPERTIES
} 
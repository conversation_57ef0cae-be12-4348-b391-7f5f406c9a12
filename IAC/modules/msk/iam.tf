# IAM policy for MSK cluster access
resource "aws_iam_policy" "msk_access_policy" {
  name        = "msk-${var.environment}-access-policy"
  description = "Policy allowing access to MSK cluster"
  
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "kafka-cluster:Connect",
          "kafka-cluster:DescribeCluster"
        ],
        Resource = aws_msk_cluster.kafka_cluster.arn
      },
      {
        Effect = "Allow",
        Action = [
          "kafka-cluster:CreateTopic",
          "kafka-cluster:DescribeTopic",
          "kafka-cluster:AlterTopic",
          "kafka-cluster:WriteData",
          "kafka-cluster:ReadData"
        ],
        Resource = "${aws_msk_cluster.kafka_cluster.arn}/topic/*"
      },
      {
        Effect = "Allow",
        Action = [
          "kafka-cluster:AlterGroup",
          "kafka-cluster:DescribeGroup"
        ],
        Resource = "${aws_msk_cluster.kafka_cluster.arn}/group/*"
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name        = "msk-${var.environment}-access-policy"
      Environment = var.environment
    }
  )
}

# Output the policy ARN so it can be attached to service account roles
output "msk_access_policy_arn" {
  description = "ARN of the MSK access policy"
  value       = aws_iam_policy.msk_access_policy.arn
} 
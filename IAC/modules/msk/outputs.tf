output "msk_cluster_arn" {
  description = "The ARN of the MSK cluster"
  value       = aws_msk_cluster.kafka_cluster.arn
}

output "msk_cluster_name" {
  description = "The name of the MSK cluster"
  value       = aws_msk_cluster.kafka_cluster.cluster_name
}

output "bootstrap_brokers" {
  description = "The connection host:port pairs for the broker nodes"
  value       = aws_msk_cluster.kafka_cluster.bootstrap_brokers
}

output "bootstrap_brokers_tls" {
  description = "The TLS connection host:port pairs for the broker nodes"
  value       = aws_msk_cluster.kafka_cluster.bootstrap_brokers_tls
}

output "zookeeper_connect_string" {
  description = "The connection string to use to connect to the Apache ZooKeeper cluster"
  value       = aws_msk_cluster.kafka_cluster.zookeeper_connect_string
}

output "security_group_id" {
  description = "The ID of the security group attached to the MSK cluster"
  value       = aws_security_group.msk_sg.id
}

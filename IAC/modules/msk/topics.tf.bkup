# Configure the Kafka provider to connect to our MSK cluster using IAM authentication
provider "kafka" {
  bootstrap_servers = split(",", aws_msk_cluster.kafka_cluster.bootstrap_brokers_tls)
  tls_enabled       = true
  skip_tls_verify   = false
  
  # Add IAM authentication
  ##sasl_mechanism   = "AWS_MSK_IAM"
  ##sasl_username    = "AWS"  # This is required but the value doesn't matter for IAM auth
  ##sasl_password    = ""     # The actual credentials come from environment variables
}

# Create Kafka topics based on the variable input
resource "kafka_topic" "topics" {
  for_each = var.kafka_topics

  name               = each.key
  replication_factor = each.value.replication_factor != null ? each.value.replication_factor : min(var.broker_count, 3)
  partitions         = each.value.partitions != null ? each.value.partitions : var.num_partitions

  config = each.value.config != null ? each.value.config : {}

  # Only create the topic after the MSK cluster is fully created and operational
  depends_on = [aws_msk_cluster.kafka_cluster]
} 
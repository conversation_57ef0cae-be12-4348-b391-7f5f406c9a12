# MSK Module with IAM Authentication

This module provisions an AWS MSK (Managed Streaming for Kafka) cluster with IAM authentication enabled, allowing EKS applications to securely connect to the Kafka cluster using IAM roles.

## Features

- Provisions an MSK cluster in private subnets
- Enables IAM authentication for secure access
- Creates Kafka topics based on configuration
- Generates an IAM policy for applications to access the MSK cluster

## Usage

1. Import the module in your Terragrunt configuration
2. Configure the MSK cluster and topics
3. Set up IRSA (IAM Roles for Service Accounts) for your applications
4. Configure your applications to use IAM authentication

## Connecting Applications to MSK using IAM

### 1. Create a Kubernetes Service Account with IRSA

```hcl
module "kafka_app_irsa" {
  source = "../modules/eks-irsa"  # Create your own IRSA module or use the example provided

  app_name           = "my-kafka-app"
  app_namespace      = "my-namespace"
  app_service_account = "my-kafka-app-sa"
  msk_access_policy_arn = module.msk.msk_access_policy_arn
}
```

### 2. Update your Kubernetes deployment to use the service account

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-kafka-app
  namespace: my-namespace
spec:
  template:
    metadata:
      labels:
        app: my-kafka-app
    spec:
      serviceAccountName: my-kafka-app-sa  # The service account with IAM role
      containers:
      - name: my-kafka-app
        image: my-kafka-app:latest
        env:
        - name: AWS_ROLE_ARN  # Optional, the SDK can discover this from the service account
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['eks.amazonaws.com/role-arn']
        - name: AWS_WEB_IDENTITY_TOKEN_FILE  # Optional, the SDK can discover this
          value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
        - name: BOOTSTRAP_SERVERS
          value: BOOTSTRAP_SERVERS_TLS_ENDPOINT_FROM_MSK
```

### 3. Configure your application to use IAM authentication

#### Java Example

```java
Properties props = new Properties();
props.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
props.put(SaslConfigs.SASL_MECHANISM, "AWS_MSK_IAM");
props.put(SaslConfigs.SASL_JAAS_CONFIG, "software.amazon.msk.auth.iam.IAMLoginModule required;");
props.put(SaslConfigs.SASL_CLIENT_CALLBACK_HANDLER_CLASS, "software.amazon.msk.auth.iam.IAMClientCallbackHandler");

// Include the AWS MSK IAM auth library in your dependencies
// implementation 'software.amazon.msk:aws-msk-iam-auth:1.1.1'
```

#### Python Example

```python
from kafka import KafkaProducer, KafkaConsumer
import boto3

# AWS MSK IAM auth requires the aws-msk-iam-auth package
# pip install aws-msk-iam-auth

producer = KafkaProducer(
    bootstrap_servers=bootstrap_servers,
    security_protocol='SASL_SSL',
    sasl_mechanism='AWS_MSK_IAM',
    sasl_oauth_token_provider=AwsMskIamTokenProvider()
)

consumer = KafkaConsumer(
    'my-topic',
    bootstrap_servers=bootstrap_servers,
    security_protocol='SASL_SSL',
    sasl_mechanism='AWS_MSK_IAM',
    sasl_oauth_token_provider=AwsMskIamTokenProvider(),
    group_id='my-consumer-group'
)
```

#### Node.js Example

```javascript
const { Kafka } = require('kafkajs');
const { AWS } = require('aws-sdk');
const { MSKAuthTokenProvider } = require('kafkajs-msk-iam-authentication');

const tokenProvider = new MSKAuthTokenProvider();

const kafka = new Kafka({
  clientId: 'my-app',
  brokers: bootstrapServers.split(','),
  ssl: true,
  sasl: {
    mechanism: 'AWS_MSK_IAM',
    authorizationIdentity: 'ANONYMOUS', // Not used for AWS MSK IAM
    tokenProvider
  }
});

// npm install kafkajs aws-sdk kafkajs-msk-iam-authentication
```

## Adding New Topics

To add new topics, update the `topics` section in your input_vars.hcl file:

```hcl
msk = {
  # Other MSK configuration...
  
  topics = {
    "new-topic-name" = {
      partitions = 6
      config = {
        "retention.ms" = "*********"  # 3 days
        "cleanup.policy" = "delete"
      }
    }
    # Add more topics as needed
  }
}
```

Then run `terragrunt apply` to create the new topics.

## Further Reading

- [AWS MSK IAM Authentication](https://docs.aws.amazon.com/msk/latest/developerguide/iam-access-control.html)
- [EKS IAM Roles for Service Accounts](https://docs.aws.amazon.com/eks/latest/userguide/iam-roles-for-service-accounts.html)
- [Kafka IAM Authentication Libraries](https://github.com/aws/aws-msk-iam-auth) 